#!/usr/bin/env node

/**
 * RouKey API Comprehensive Test Script
 * Tests all available API endpoints and functionality with your actual API key
 * Updated to use your provided API key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM
 */

const API_KEY = 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM';
const BASE_URL = 'https://roukey.online'; // Your actual deployed domain
const API_BASE = `${BASE_URL}/api/external/v1`;

// Comprehensive test scenarios for all RouKey features
const chatTestCases = [
  {
    name: 'Simple greeting',
    messages: [{ role: 'user', content: 'Hello! How are you?' }],
    stream: false
  },
  {
    name: 'Code generation request',
    messages: [{ role: 'user', content: 'Write a Python function to calculate fibonacci numbers' }],
    stream: false
  },
  {
    name: 'Complex reasoning task',
    messages: [{ role: 'user', content: 'Explain the differences between machine learning, deep learning, and artificial intelligence' }],
    stream: false
  },
  {
    name: 'Streaming response test',
    messages: [{ role: 'user', content: 'Tell me a short story about space exploration' }],
    stream: true
  },
  {
    name: 'Role-based routing test',
    messages: [{ role: 'user', content: 'Explain quantum computing principles' }],
    role: 'science_expert',
    stream: false
  },
  {
    name: 'Agent mode complexity test',
    messages: [{ role: 'user', content: 'Design a comprehensive business plan for a sustainable energy startup, including market analysis, financial projections, and technical implementation strategy' }],
    stream: false
  },
  {
    name: 'Temperature test (creative)',
    messages: [{ role: 'user', content: 'Write a creative poem about technology' }],
    temperature: 1.2,
    stream: false
  },
  {
    name: 'Temperature test (factual)',
    messages: [{ role: 'user', content: 'What is the speed of light in vacuum?' }],
    temperature: 0.1,
    stream: false
  },
  {
    name: 'Multi-turn conversation',
    messages: [
      { role: 'user', content: 'What is blockchain technology?' },
      { role: 'assistant', content: 'Blockchain is a distributed ledger technology...' },
      { role: 'user', content: 'How does it ensure security?' }
    ],
    stream: false
  },
  {
    name: 'Long content with streaming',
    messages: [{ role: 'user', content: 'Provide a comprehensive guide to setting up a modern web development environment with React, Node.js, and TypeScript' }],
    stream: true
  }
];

// Helper function to make API requests with proper error handling
async function makeApiRequest(endpoint, method = 'GET', body = null, description = '') {
  console.log(`\n🔗 ${description || method} ${endpoint}`);
  console.log('─'.repeat(50));

  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
      'User-Agent': 'RouKey-Comprehensive-Test/1.0'
    }
  };

  if (body && method !== 'GET') {
    options.body = JSON.stringify(body);
    console.log('📤 Request Body:', JSON.stringify(body, null, 2));
  }

  try {
    const startTime = Date.now();
    const response = await fetch(endpoint, options);
    const duration = Date.now() - startTime;

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Duration: ${duration}ms`);

    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
      console.log('📥 Response:', JSON.stringify(responseData, null, 2));
    } catch {
      console.log('📥 Raw Response:', responseText);
    }

    return { success: response.ok, status: response.status, data: responseData || responseText, response };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Chat completions test function
async function makeChatRequest(testCase, testNumber) {
  console.log(`\n🧪 Chat Test ${testNumber}: ${testCase.name}`);
  console.log('=' .repeat(50));

  const requestBody = {
    messages: testCase.messages,
    stream: testCase.stream || false,
    ...(testCase.temperature && { temperature: testCase.temperature }),
    ...(testCase.role && { role: testCase.role }),
    max_tokens: 500
  };

  console.log('📤 Request:', JSON.stringify(requestBody, null, 2));
  console.log('🔑 Using API Key:', `${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);

  try {
    const startTime = Date.now();
    const endpoint = `${API_BASE}/chat/completions`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Test-Script/1.0'
      },
      body: JSON.stringify(requestBody)
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Duration: ${duration}ms`);

    // Show important headers
    const importantHeaders = ['content-type', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-request-id'];
    const headers = Object.fromEntries(response.headers.entries());
    const filteredHeaders = Object.fromEntries(
      Object.entries(headers).filter(([key]) => importantHeaders.includes(key.toLowerCase()))
    );
    console.log('📋 Headers:', filteredHeaders);

    if (testCase.stream) {
      console.log('📡 Streaming response:');
      console.log('─'.repeat(30));

      if (response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            process.stdout.write(chunk);
            fullResponse += chunk;
          }
        } catch (streamError) {
          console.error('❌ Streaming error:', streamError.message);
        }

        console.log('\n─'.repeat(30));
        console.log(`📏 Total response length: ${fullResponse.length} characters`);
      } else {
        console.log('❌ No response body for streaming');
      }
    } else {
      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
        console.log('📥 Response:', JSON.stringify(responseData, null, 2));

        // Check for RouKey-specific features
        if (responseData.error && responseData.error.code === 'require_async_processing') {
          console.log('🤖 MULTI-ROLE/AGENT MODE DETECTED - Async processing required!');
          console.log(`💡 Reason: ${responseData.error.suggestion.reason}`);
          console.log(`🔗 Async URL: ${responseData.error.suggestion.async_submit_url}`);
        } else if (responseData.error && responseData.error.code === 'suggest_async_processing') {
          console.log('⚠️  Complex task detected - Async processing suggested');
          console.log(`💡 Reason: ${responseData.error.suggestion.reason}`);
        }

        if (responseData.choices && responseData.choices[0]) {
          const content = responseData.choices[0].message?.content || '';
          console.log(`📏 Response length: ${content.length} characters`);
        }

        // Show RouKey metadata
        if (responseData.rokey_metadata) {
          console.log('🎭 RouKey Metadata:');
          if (responseData.rokey_metadata.roles_used) {
            console.log(`   Roles used: ${responseData.rokey_metadata.roles_used.join(', ')}`);
          }
          if (responseData.rokey_metadata.routing_strategy) {
            console.log(`   Routing strategy: ${responseData.rokey_metadata.routing_strategy}`);
          }
          if (responseData.rokey_metadata.provider_used) {
            console.log(`   Provider: ${responseData.rokey_metadata.provider_used}`);
          }
        }
      } catch (parseError) {
        console.log('📥 Raw Response (not JSON):', responseText);
      }
    }

    if (!response.ok) {
      console.log('❌ Request failed');
      return false;
    }

    console.log('✅ Request successful');
    return true;

  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

// Test all API endpoints
async function testApiEndpoints() {
  console.log('\n🔧 TESTING ALL API ENDPOINTS');
  console.log('='.repeat(60));

  const results = [];

  // Test 1: Get API Documentation
  console.log('\n📚 Testing API Documentation...');
  const docsResult = await makeApiRequest(`${API_BASE}/docs`, 'GET', null, 'Get API Documentation');
  results.push({ name: 'API Documentation', success: docsResult.success });

  // Test 2: List Providers
  console.log('\n🏢 Testing Providers Endpoint...');
  const providersResult = await makeApiRequest(`${API_BASE}/providers`, 'GET', null, 'List Supported Providers');
  results.push({ name: 'List Providers', success: providersResult.success });

  // Test 3: List Models
  console.log('\n🤖 Testing Models Endpoint...');
  const modelsResult = await makeApiRequest(`${API_BASE}/models`, 'GET', null, 'List Available Models');
  results.push({ name: 'List Models', success: modelsResult.success });

  // Test 4: List Configurations
  console.log('\n⚙️  Testing Configurations Endpoint...');
  const configsResult = await makeApiRequest(`${API_BASE}/configs`, 'GET', null, 'List Configurations');
  results.push({ name: 'List Configurations', success: configsResult.success });

  // Test 5: Usage Analytics
  console.log('\n📊 Testing Usage Analytics...');
  const usageResult = await makeApiRequest(`${API_BASE}/usage`, 'GET', null, 'Get Usage Analytics');
  results.push({ name: 'Usage Analytics', success: usageResult.success });

  // Test 6: List API Keys
  console.log('\n🔑 Testing API Keys Endpoint...');
  const apiKeysResult = await makeApiRequest(`${API_BASE}/api-keys`, 'GET', null, 'List API Keys');
  results.push({ name: 'List API Keys', success: apiKeysResult.success });

  return results;
}

async function testConnectivity() {
  console.log('🔍 Testing connectivity to production deployment...');

  try {
    const response = await fetch(`${BASE_URL}`, {
      method: 'GET',
      headers: {
        'User-Agent': 'RouKey-Test-Script/1.0'
      }
    });

    console.log(`📊 Base URL Status: ${response.status} ${response.statusText}`);

    if (response.ok || response.status === 404) { // 404 is fine for base URL
      console.log('✅ Production deployment is accessible');
      return true;
    } else {
      console.log('⚠️  Production deployment returned unexpected status');
      return false;
    }
  } catch (error) {
    console.error('❌ Cannot reach production deployment:', error.message);
    return false;
  }
}

// Test configuration management features
async function testConfigurationManagement() {
  console.log('\n⚙️  TESTING CONFIGURATION MANAGEMENT');
  console.log('='.repeat(60));

  const results = [];

  // Get existing configurations first
  const configsResult = await makeApiRequest(`${API_BASE}/configs`, 'GET', null, 'List Existing Configurations');
  results.push({ name: 'List Configurations', success: configsResult.success });

  let testConfigId = null;

  if (configsResult.success && configsResult.data && configsResult.data.data && configsResult.data.data.length > 0) {
    testConfigId = configsResult.data.data[0].id;
    console.log(`\n🎯 Using existing config ID: ${testConfigId}`);

    // Test getting specific configuration
    const getConfigResult = await makeApiRequest(`${API_BASE}/configs/${testConfigId}`, 'GET', null, 'Get Specific Configuration');
    results.push({ name: 'Get Configuration', success: getConfigResult.success });

    // Test getting configuration keys
    const getKeysResult = await makeApiRequest(`${API_BASE}/configs/${testConfigId}/keys`, 'GET', null, 'List Configuration Keys');
    results.push({ name: 'List Configuration Keys', success: getKeysResult.success });

    // Test getting routing configuration
    const getRoutingResult = await makeApiRequest(`${API_BASE}/configs/${testConfigId}/routing`, 'GET', null, 'Get Routing Configuration');
    results.push({ name: 'Get Routing Configuration', success: getRoutingResult.success });
  } else {
    console.log('\n⚠️  No existing configurations found. Skipping configuration-specific tests.');
  }

  return results;
}

// Test advanced features
async function testAdvancedFeatures() {
  console.log('\n🚀 TESTING ADVANCED FEATURES');
  console.log('='.repeat(60));

  const results = [];

  // Test usage analytics with different parameters
  const usageParams = new URLSearchParams({
    granularity: 'day',
    start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    end_date: new Date().toISOString()
  });

  const usageResult = await makeApiRequest(`${API_BASE}/usage?${usageParams}`, 'GET', null, 'Get Usage Analytics (7 days)');
  results.push({ name: 'Usage Analytics with Parameters', success: usageResult.success });

  return results;
}

async function debugApiKey() {
  console.log('\n🔍 API KEY VALIDATION');
  console.log('='.repeat(60));
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);
  console.log(`📏 Key Length: ${API_KEY.length}`);
  console.log(`🏷️  Key Format: ${API_KEY.startsWith('rk_live_') ? '✅ Valid live key prefix' : '❌ Invalid prefix'}`);

  // Test basic authentication with a simple endpoint
  try {
    console.log('\n🧪 Testing API Key Authentication...');
    const response = await fetch(`${API_BASE}/providers`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Auth-Test/1.0'
      }
    });

    console.log(`📊 Auth Test Status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      console.log('✅ API Key authentication successful!');
      const data = await response.json();
      console.log(`📊 Providers available: ${data.data ? data.data.length : 'unknown'}`);
    } else {
      const errorText = await response.text();
      console.log('❌ API Key authentication failed');
      console.log(`📥 Error Response:`, errorText);
    }

  } catch (error) {
    console.error('❌ Auth test request failed:', error.message);
  }
}

async function runComprehensiveTests() {
  console.log('🚀 STARTING COMPREHENSIVE ROKEY API TESTS');
  console.log('='.repeat(60));
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`📡 API Base: ${API_BASE}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);

  // Test connectivity first
  const isConnected = await testConnectivity();
  if (!isConnected) {
    console.log('❌ Cannot proceed with tests - deployment not accessible');
    return;
  }

  // Debug API key first
  await debugApiKey();

  console.log('\n⏳ Waiting 2 seconds before starting comprehensive tests...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test all API endpoints
  const endpointResults = await testApiEndpoints();

  console.log('\n⏳ Waiting 2 seconds before configuration tests...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test configuration management
  const configResults = await testConfigurationManagement();

  console.log('\n⏳ Waiting 2 seconds before advanced feature tests...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test advanced features
  const advancedResults = await testAdvancedFeatures();

  console.log('\n⏳ Waiting 3 seconds before chat completion tests...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Test chat completions
  console.log('\n💬 TESTING CHAT COMPLETIONS');
  console.log('='.repeat(60));

  let chatSuccessCount = 0;
  let totalChatTests = chatTestCases.length;

  for (let i = 0; i < chatTestCases.length; i++) {
    const success = await makeChatRequest(chatTestCases[i], i + 1);
    if (success) chatSuccessCount++;

    // Wait between requests to avoid rate limiting
    if (i < chatTestCases.length - 1) {
      console.log('\n⏳ Waiting 3 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Final comprehensive summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPREHENSIVE TEST SUMMARY');
  console.log('='.repeat(60));

  console.log('\n🔧 API Endpoints Results:');
  endpointResults.forEach(result => {
    console.log(`   ${result.success ? '✅' : '❌'} ${result.name}`);
  });

  console.log('\n⚙️  Configuration Management Results:');
  configResults.forEach(result => {
    console.log(`   ${result.success ? '✅' : '❌'} ${result.name}`);
  });

  console.log('\n🚀 Advanced Features Results:');
  advancedResults.forEach(result => {
    console.log(`   ${result.success ? '✅' : '❌'} ${result.name}`);
  });

  console.log('\n💬 Chat Completions Results:');
  console.log(`   ${chatSuccessCount}/${totalChatTests} tests passed`);

  // Calculate totals
  const endpointSuccessCount = endpointResults.filter(r => r.success).length;
  const configSuccessCount = configResults.filter(r => r.success).length;
  const advancedSuccessCount = advancedResults.filter(r => r.success).length;

  const totalApiTests = endpointResults.length + configResults.length + advancedResults.length;
  const totalApiSuccessful = endpointSuccessCount + configSuccessCount + advancedSuccessCount;

  console.log('\n📈 SUMMARY BY CATEGORY:');
  console.log(`📡 API Endpoints: ${endpointSuccessCount}/${endpointResults.length} (${((endpointSuccessCount/endpointResults.length)*100).toFixed(1)}%)`);
  console.log(`⚙️  Configuration: ${configSuccessCount}/${configResults.length} (${configResults.length > 0 ? ((configSuccessCount/configResults.length)*100).toFixed(1) : 'N/A'}%)`);
  console.log(`🚀 Advanced Features: ${advancedSuccessCount}/${advancedResults.length} (${((advancedSuccessCount/advancedResults.length)*100).toFixed(1)}%)`);
  console.log(`💬 Chat Completions: ${chatSuccessCount}/${totalChatTests} (${((chatSuccessCount/totalChatTests)*100).toFixed(1)}%)`);

  const totalTests = totalApiTests + totalChatTests;
  const totalSuccessful = totalApiSuccessful + chatSuccessCount;

  console.log(`\n🎯 OVERALL SUCCESS RATE: ${((totalSuccessful / totalTests) * 100).toFixed(1)}% (${totalSuccessful}/${totalTests})`);
  console.log(`📅 Completed at: ${new Date().toISOString()}`);

  if (totalSuccessful === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! Your RouKey API is working perfectly!');
    console.log('✨ Your API key has full access to all RouKey features.');
    console.log('🚀 You can now confidently integrate RouKey into your applications!');
  } else if (totalSuccessful / totalTests >= 0.8) {
    console.log('\n✅ Most tests passed! Your RouKey API is working well.');
    console.log('⚠️  Some minor issues detected - check logs for details.');
  } else {
    console.log('\n⚠️  Several tests failed. Check the logs above for details.');
    console.log('💡 This might indicate configuration issues or missing permissions.');
    console.log('📞 Contact <NAME_EMAIL> if issues persist.');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the comprehensive tests
runComprehensiveTests().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
