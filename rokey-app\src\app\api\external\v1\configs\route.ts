import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

// Validation schemas
const CreateConfigSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  routing_strategy: z.enum(['load_balancing', 'role_routing', 'agent_mode']).optional().default('load_balancing'),
  settings: z.object({
    temperature: z.number().min(0).max(2).optional(),
    max_tokens: z.number().int().positive().optional(),
    top_p: z.number().min(0).max(1).optional(),
    frequency_penalty: z.number().min(-2).max(2).optional(),
    presence_penalty: z.number().min(-2).max(2).optional(),
  }).optional().default({})
});

const UpdateConfigSchema = CreateConfigSchema.partial();

// GET /api/external/v1/configs - List all configurations
export async function GET(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Get user's configurations
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: configs, error } = await supabase
      .from('custom_api_configs')
      .select(`
        id,
        name,
        description,
        routing_strategy,
        settings,
        created_at,
        updated_at
      `)
      .eq('user_id', userConfig!.user_id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching configs:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to fetch configurations',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 3. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'config_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      object: 'list',
      data: configs || [],
      has_more: false
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in configs GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// POST /api/external/v1/configs - Create new configuration
export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = CreateConfigSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request data',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const configData = validationResult.data;

    // 3. Create configuration
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: newConfig, error } = await supabase
      .from('custom_api_configs')
      .insert({
        user_id: userConfig!.user_id,
        name: configData.name,
        description: configData.description || '',
        routing_strategy: configData.routing_strategy,
        settings: configData.settings
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating config:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to create configuration',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 201,
        modelUsed: 'config_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: newConfig.id,
      object: 'config',
      name: newConfig.name,
      description: newConfig.description,
      routing_strategy: newConfig.routing_strategy,
      settings: newConfig.settings,
      created_at: newConfig.created_at,
      updated_at: newConfig.updated_at
    }, { 
      status: 201,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in configs POST API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
