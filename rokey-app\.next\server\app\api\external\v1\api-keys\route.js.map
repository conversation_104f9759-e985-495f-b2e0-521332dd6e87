{"version": 3, "file": "app/api/external/v1/api-keys/route.js", "mappings": "qFAAA,wDCAA,wHGAA,yTFOO,IAAMA,EAAU,OAAO,EAEP,IAAIC,EAAAA,CAAoBA,CAGzCC,EAAqBC,EAAAA,CAAAA,CAAAA,IAHPC,EAGe,CAAC,CAClCC,UAAWF,EAAAA,CAAAA,CAAAA,MAAQ,GAAGG,IAAI,GAC1BC,SAAUJ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGK,GAAG,CAAC,GAAGC,GAAG,CAAC,KAChCC,WAAYP,EAAAA,CAAAA,CAAAA,MAAQ,GAAGQ,QAAQ,GAAGC,QAAQ,GAC1CC,YAAaV,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACpBW,KAAMX,EAAAA,CAAAA,CAAAA,OAAS,GAAGS,QAAQ,GAAGG,OAAO,CAAC,IACrCC,UAAWb,EAAAA,CAAAA,CAAAA,OAAS,GAAGS,QAAQ,GAAGG,OAAO,EAAC,GAC1CE,WAAYd,EAAAA,CAAAA,CAAAA,OAAS,GAAGS,QAAQ,GAAGG,OAAO,EAAC,EAC7C,GAAGH,QAAQ,GAAGG,OAAO,CAAC,CACpBD,MAAM,EACNE,WAAW,EACXC,YAAY,CACd,GACAC,YAAaf,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,GAAGgB,EAAE,IAAIP,QAAQ,GAAGG,OAAO,CAAC,EAAE,EAC3DK,gBAAiBjB,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,IAAIS,QAAQ,GAAGG,OAAO,CAAC,EAAE,CAC5D,GAgBO,eAAeM,EAAIC,CAAoB,EAC5C,GAAI,CAEF,IAAMC,EAAa,MAAMnB,EAAeoB,kBAADpB,CAAoB,CAACkB,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGb,EAGxC,cAAEc,CAAY,CAAE,CAAG,IAAIC,IAAIhB,EAAQiB,GAAG,EACtCC,EAAWH,EAAaI,GAAG,CAAC,aAC5BT,EAASK,EAAaI,GAAG,CAAC,UAK5BC,EAAQC,CAFKC,EAAAA,EAAAA,EAAAA,CAAqCA,CAACtB,GAGpDuB,IAAI,CAAC,2BACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;MAkBT,CAAC,EACAC,EAAE,CAAC,6BAA8BZ,EAAYa,OAAO,EACpDC,KAAK,CAAC,aAAc,CAAEC,WAAW,CAAM,GAGtCV,IACFE,EAAQA,EAAMK,EADF,CACK,uBAAwBP,EAAAA,EAEvCR,IACFU,EAAQA,EADE,EACM,CAAC,SAAUV,EAAAA,EAG7B,GAAM,CAAEmB,KAAMC,CAAO,OAAExB,CAAK,CAAE,CAAG,MAAMc,EAEvC,GAAId,EAEF,KAFS,EAEFF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,2BACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,IAAMqB,EAAkB,CAACD,GAAW,IAAIE,GAAG,CAACC,GAAQ,EAClDC,CADkD,EAC9CD,EAAIC,EAAE,CACVC,OAAQ,UACRlD,SAAUgD,EAAIhD,QAAQ,CACtBmD,WAAYH,EAAIG,UAAU,CAC1BC,WAAY,GAAGJ,EAAIG,UAAU,CAAC,CAAC,EAAE,IAAIE,MAAM,CAAC,IAAI,IAAI,CAAC,CACrD/C,YAAa0C,EAAI1C,WAAW,CAC5BK,YAAaqC,EAAIrC,WAAW,CAC5BE,gBAAiBmC,EAAInC,eAAe,CACpCyC,eAAgBN,EAAIM,cAAc,CAClCC,aAAcP,EAAIO,YAAY,CAC9B9B,OAAQuB,EAAIvB,MAAM,CAClBtB,WAAY6C,EAAI7C,UAAU,CAC1BqD,WAAYR,EAAIQ,UAAU,CAC1BC,WAAYT,EAAIS,UAAU,CAC1BC,OAAQ,CACNT,GAAI,EAAKU,kBAAkB,CAASV,EAAE,CACtCW,KAAM,EAAKD,kBAAkB,CAASC,IAAI,EAE9C,GAcA,OAXA/D,EAAegE,WAAW,CACxBlC,EACAZ,EACA,CACEW,CAJU7B,UAIE,IACZiE,UAAW,qBACXC,aAAc,WAChB,EACAlC,GACAmC,KAAK,CAACC,QAAQ5C,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB8B,OAAQ,OACRN,KAAME,EACNoB,UAAU,EACVC,YAAarB,EAAgBsB,MAAM,EAClC,CACDC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,qBAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOhD,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAe6C,EAAKvD,CAAoB,EAC7C,GAAI,CAEF,IAAMC,EAAa,MAAMnB,EAAeoB,kBAADpB,CAAoB,CAACkB,GAE5D,GAAI,CAACC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAEC,OAAQT,EAAWU,UAAU,EAAI,GAAI,GAI3C,GAAM,CAAEC,YAAU,YAAEC,CAAU,CAAEC,WAAS,CAAE,CAAGb,EAGxCuD,EAAO,MAAMxD,EAAQK,IAAI,GACzBoD,EAAmB7E,EAAmB8E,SAAS,CAACF,GAEtD,GAAI,CAACC,EAAiBtD,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,uBACTC,KAAM,mBACNC,KAAM,qBACNkD,QAASF,EAAiBnD,KAAK,CAACsD,MAAM,CAE1C,EACA,CAAElD,OAAQ,GAAI,GAIlB,IAAMmD,EAAUJ,EAAiB5B,IAAI,CAG/BR,EAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAACtB,GAEjD,CAAE6B,KAAMc,CAAM,CAAErC,MAAOwD,CAAW,CAAE,CAAG,MAAMzC,EAChDE,IAAI,CAAC,sBACLC,MAAM,CAAC,YACPC,EAAE,CAAC,KAAMoC,EAAQ9E,SAAS,EAC1B0C,EAAE,CAAC,UAAWZ,EAAYa,OAAO,EACjCqC,MAAM,GAET,GAAID,GAAe,CAACnB,EAClB,MAD0B,CACnBvC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,0BACTC,KAAM,kBACNC,KAAM,kBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAKlB,GAAM,SAAEsD,CAAO,QAAEC,CAAM,YAAEC,CAAU,MAAEC,CAAI,CAAE,CAAG,MAAMC,EAAAA,CAAeA,CAACC,cAAc,GAC5EC,EAAkB,MAAMF,EAAAA,CAAeA,CAACG,aAAa,CAACL,GAGtD,CAAErC,KAAM2C,CAAS,CAAElE,MAAOmE,CAAW,CAAE,CAAG,MAAMpD,EACnDE,IAAI,CAAC,2BACLmD,MAAM,CAAC,CACNhD,QAASb,EAAYa,OAAO,CAC5BiD,qBAAsBd,EAAQ9E,SAAS,CACvCE,SAAU4E,EAAQ5E,QAAQ,CAC1BmD,WAAY6B,EACZW,SAAUT,EACVU,qBAAsBP,EACtB/E,YAAasE,EAAQtE,WAAW,CAChCK,YAAaiE,EAAQjE,WAAW,CAChCE,gBAAiB+D,EAAQ/D,eAAe,CACxCV,WAAYyE,EAAQzE,UAAU,EAAI,IACpC,GACCoC,MAAM,GACNuC,MAAM,GAET,GAAIU,EAEF,OAAOrE,EAAAA,EAFQ,CAEKC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,2BACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,GAgBlB,OAXA5B,EAAegE,WAAW,CACxBlC,EACAZ,EACA,CACEW,CAJU7B,UAIE,IACZiE,UAAW,qBACXC,aAAc,WAChB,EACAlC,GACAmC,KAAK,CAACC,QAAQ5C,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB6B,GAAIsC,EAAUtC,EAAE,CAChBC,OAAQ,UACRlD,SAAUuF,EAAUvF,QAAQ,CAC5B6F,QAASd,EACT5B,WAAYoC,EAAUpC,UAAU,CAChC7C,YAAaiF,EAAUjF,WAAW,CAClCK,YAAa4E,EAAU5E,WAAW,CAClCE,gBAAiB0E,EAAU1E,eAAe,CAC1CY,OAAQ8D,EAAU9D,MAAM,CACxBtB,WAAYoF,EAAUpF,UAAU,CAChCqD,WAAY+B,EAAU/B,UAAU,CAChCE,OAAQ,CACNT,GAAIS,EAAOT,EAAE,CACbW,KAAMF,EAAOE,IAAI,EAEnBkC,QAAS,wEACX,EAAG,CACDrE,OAAQ,IACR4C,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,qBAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOhD,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAEC,OAAQ,GAAI,EAElB,CACF,CAGO,eAAesE,IACpB,OAAO,IAAI5E,EAAAA,EAAYA,CAAC,KAAM,CAC5BM,OAAQ,IACR4C,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,qBAChC,+BAAgC,wCAClC,CACF,EACF,CA3T2BzE,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CAClCI,SAAUJ,EAAAA,CAAAA,CAAAA,MAAQ,GAAGK,GAAG,CAAC,GAAGC,GAAG,CAAC,KAAKG,QAAQ,GAC7CoB,OAAQ7B,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,SAAU,WAAW,EAAES,QAAQ,GAC/CF,WAAYP,EAAAA,CAAAA,CAAAA,MAAQ,GAAGQ,QAAQ,GAAG4F,QAAQ,GAAG3F,QAAQ,GACrDC,YAAaV,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACpBW,KAAMX,EAAAA,CAAAA,CAAAA,OAAS,GAAGS,QAAQ,GAC1BI,UAAWb,EAAAA,CAAAA,CAAAA,OAAS,GAAGS,QAAQ,GAC/BK,WAAYd,EAAAA,CAAAA,CAAAA,OAAS,GAAGS,QAAQ,EAClC,GAAGA,QAAQ,GACXM,YAAaf,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,GAAGgB,EAAE,IAAIP,QAAQ,GAC9CQ,gBAAiBjB,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,IAAIS,QAAQ,EAC/C,GCjCA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,uCACA,qCACA,iBACA,+CACA,CAAK,CACL,6FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,gFACA,GAFA,2BAEA,2BACA,OACI,QAA8B,EAClC,uCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC,4DCsBI,SAASgC,EAAsCtB,CAAoB,EACxE,MAAOkF,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,KACPjE,GACSnB,CADO,CACCoF,OAAO,CAACjE,GAAG,CAAC0B,IAAOwC,MAEpCC,IAAIzC,CAAY,CAAEwC,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAO3C,CAAY,CAAE0C,CAAsB,EAG3C,CACF,CACF,EAEJ", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/app/api/external/v1/api-keys/route.ts", "webpack://_N_E/./src/app/api/external/v1/api-keys/route.ts?f44e", "webpack://_N_E/", "webpack://_N_E/./src/lib/supabase/server.ts"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { Api<PERSON>eyGenerator } from '@/lib/userApiKeys/apiKeyGenerator';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\n// Validation schema for creating API keys\nconst CreateApiKeySchema = z.object({\n  config_id: z.string().uuid(),\n  key_name: z.string().min(1).max(100),\n  expires_at: z.string().datetime().optional(),\n  permissions: z.object({\n    chat: z.boolean().optional().default(true),\n    streaming: z.boolean().optional().default(true),\n    all_models: z.boolean().optional().default(true)\n  }).optional().default({\n    chat: true,\n    streaming: true,\n    all_models: true\n  }),\n  allowed_ips: z.array(z.string().ip()).optional().default([]),\n  allowed_domains: z.array(z.string()).optional().default([])\n});\n\nconst UpdateApiKeySchema = z.object({\n  key_name: z.string().min(1).max(100).optional(),\n  status: z.enum(['active', 'inactive']).optional(),\n  expires_at: z.string().datetime().nullable().optional(),\n  permissions: z.object({\n    chat: z.boolean().optional(),\n    streaming: z.boolean().optional(),\n    all_models: z.boolean().optional()\n  }).optional(),\n  allowed_ips: z.array(z.string().ip()).optional(),\n  allowed_domains: z.array(z.string()).optional()\n});\n\n// GET /api/external/v1/api-keys - List user's generated API keys\nexport async function GET(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Get query parameters\n    const { searchParams } = new URL(request.url);\n    const configId = searchParams.get('config_id');\n    const status = searchParams.get('status');\n\n    // 3. Build query\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    let query = supabase\n      .from('user_generated_api_keys')\n      .select(`\n        id,\n        key_name,\n        key_prefix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        status,\n        expires_at,\n        created_at,\n        updated_at,\n        custom_api_configs!inner(\n          id,\n          name,\n          user_id\n        )\n      `)\n      .eq('custom_api_configs.user_id', userConfig!.user_id)\n      .order('created_at', { ascending: false });\n\n    // Apply filters\n    if (configId) {\n      query = query.eq('custom_api_config_id', configId);\n    }\n    if (status) {\n      query = query.eq('status', status);\n    }\n\n    const { data: apiKeys, error } = await query;\n\n    if (error) {\n      console.error('Error fetching API keys:', error);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to fetch API keys',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 4. Transform data for response (mask keys for security)\n    const transformedKeys = (apiKeys || []).map(key => ({\n      id: key.id,\n      object: 'api_key',\n      key_name: key.key_name,\n      key_prefix: key.key_prefix,\n      masked_key: `${key.key_prefix}_${'*'.repeat(28)}xxxx`,\n      permissions: key.permissions,\n      allowed_ips: key.allowed_ips,\n      allowed_domains: key.allowed_domains,\n      total_requests: key.total_requests,\n      last_used_at: key.last_used_at,\n      status: key.status,\n      expires_at: key.expires_at,\n      created_at: key.created_at,\n      updated_at: key.updated_at,\n      config: {\n        id: (key.custom_api_configs as any).id,\n        name: (key.custom_api_configs as any).name\n      }\n    }));\n\n    // 5. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'api_key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      object: 'list',\n      data: transformedKeys,\n      has_more: false,\n      total_count: transformedKeys.length\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in API keys GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/external/v1/api-keys - Create new API key\nexport async function POST(request: NextRequest) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = CreateApiKeySchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request data',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const keyData = validationResult.data;\n\n    // 3. Verify config belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: config, error: configError } = await supabase\n      .from('custom_api_configs')\n      .select('id, name')\n      .eq('id', keyData.config_id)\n      .eq('user_id', userConfig!.user_id)\n      .single();\n\n    if (configError || !config) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Configuration not found',\n            type: 'not_found_error',\n            code: 'config_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 4. Generate the API key\n    const { fullKey, prefix, secretPart, hash } = await ApiKeyGenerator.generateApiKey();\n    const encryptedSuffix = await ApiKeyGenerator.encryptSuffix(secretPart);\n\n    // 5. Create API key record\n    const { data: newApiKey, error: createError } = await supabase\n      .from('user_generated_api_keys')\n      .insert({\n        user_id: userConfig!.user_id,\n        custom_api_config_id: keyData.config_id,\n        key_name: keyData.key_name,\n        key_prefix: prefix,\n        key_hash: hash,\n        encrypted_key_suffix: encryptedSuffix,\n        permissions: keyData.permissions,\n        allowed_ips: keyData.allowed_ips,\n        allowed_domains: keyData.allowed_domains,\n        expires_at: keyData.expires_at || null\n      })\n      .select()\n      .single();\n\n    if (createError) {\n      console.error('Error creating API key:', createError);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to create API key',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 6. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 201,\n        modelUsed: 'api_key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: newApiKey.id,\n      object: 'api_key',\n      key_name: newApiKey.key_name,\n      api_key: fullKey, // Full key - only shown once!\n      key_prefix: newApiKey.key_prefix,\n      permissions: newApiKey.permissions,\n      allowed_ips: newApiKey.allowed_ips,\n      allowed_domains: newApiKey.allowed_domains,\n      status: newApiKey.status,\n      expires_at: newApiKey.expires_at,\n      created_at: newApiKey.created_at,\n      config: {\n        id: config.id,\n        name: config.name\n      },\n      warning: 'Save this API key now - this is the only time you will see it in full!'\n    }, { \n      status: 201,\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in API keys POST API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\api-keys\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/api-keys/route\",\n        pathname: \"/api/external/v1/api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/api-keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/api-keys/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/api-keys/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/api-keys/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n"], "names": ["runtime", "ApiKeyAuthMiddleware", "CreateApiKeySchema", "z", "authMiddleware", "config_id", "uuid", "key_name", "min", "max", "expires_at", "datetime", "optional", "permissions", "chat", "default", "streaming", "all_models", "allowed_ips", "ip", "allowed_domains", "GET", "request", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "status", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "searchParams", "URL", "url", "configId", "get", "query", "supabase", "createSupabaseServerClientFromRequest", "from", "select", "eq", "user_id", "order", "ascending", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformed<PERSON><PERSON><PERSON>", "map", "key", "id", "object", "key_prefix", "masked_key", "repeat", "total_requests", "last_used_at", "created_at", "updated_at", "config", "custom_api_configs", "name", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "has_more", "total_count", "length", "headers", "POST", "body", "validationResult", "safeParse", "details", "errors", "keyData", "config<PERSON><PERSON>r", "single", "<PERSON><PERSON><PERSON>", "prefix", "secretPart", "hash", "ApiKeyGenerator", "generateApiKey", "encryptedSuffix", "encryptSuffix", "newApiKey", "createError", "insert", "custom_api_config_id", "key_hash", "encrypted_key_suffix", "api_key", "warning", "OPTIONS", "nullable", "createServerClient", "process", "cookies", "value", "set", "options", "remove"], "sourceRoot": "", "ignoreList": []}