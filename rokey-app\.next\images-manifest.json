{"version": 1, "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image", "loader": "default", "loaderFile": "", "domains": [], "disableStaticImages": false, "minimumCacheTTL": 60, "formats": ["image/webp", "image/avif"], "dangerouslyAllowSVG": true, "contentSecurityPolicy": "default-src 'self'; script-src 'none'; sandbox;", "contentDispositionType": "attachment", "remotePatterns": [{"protocol": "https", "hostname": "^(?:^(?:raw\\.githubusercontent\\.com)$)$", "port": "", "pathname": "^(?:\\/lobehub\\/lobe-icons(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}, {"protocol": "https", "hostname": "^(?:^(?:registry\\.npmmirror\\.com)$)$", "port": "", "pathname": "^(?:\\/@lobehub\\/icons-static-png(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}, {"protocol": "https", "hostname": "^(?:^(?:cdn\\.jsdelivr\\.net)$)$", "port": "", "pathname": "^(?:\\/npm\\/simple-icons@latest\\/icons(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}, {"protocol": "https", "hostname": "^(?:^(?:cdn\\.jsdelivr\\.net)$)$", "port": "", "pathname": "^(?:\\/npm\\/simple-icons@v11\\/icons(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}, {"protocol": "https", "hostname": "^(?:^(?:images\\.unsplash\\.com)$)$", "port": "", "pathname": "^(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?))$"}, {"protocol": "https", "hostname": "^(?:^(?:cloud\\.gmelius\\.com)$)$", "port": "", "pathname": "^(?:\\/public\\/logos(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}, {"protocol": "https", "hostname": "^(?:^(?:upload\\.wikimedia\\.org)$)$", "port": "", "pathname": "^(?:\\/wikipedia\\/commons(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}, {"protocol": "https", "hostname": "^(?:^(?:kstatic\\.googleusercontent\\.com)$)$", "port": "", "pathname": "^(?:\\/files(?:\\/(?!\\.{1,2}(?:\\/|$))(?:(?:(?!(?:^|\\/)\\.{1,2}(?:\\/|$)).)*?)|$))$"}], "unoptimized": false, "sizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840, 16, 32, 48, 64, 96, 128, 256, 384]}}