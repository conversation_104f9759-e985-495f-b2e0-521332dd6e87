import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { encrypt } from '@/lib/encryption';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

// Validation schema for creating provider keys
const CreateProviderKeySchema = z.object({
  provider: z.enum(['openai', 'anthropic', 'google', 'cohere', 'mistral', 'perplexity', 'groq', 'together', 'fireworks', 'deepseek']),
  api_key: z.string().min(1),
  model_id: z.string().uuid().optional(),
  label: z.string().min(1).max(100),
  temperature: z.number().min(0).max(2).optional().default(0.7),
  is_default_general_chat_model: z.boolean().optional().default(false)
});

const UpdateProviderKeySchema = z.object({
  api_key: z.string().min(1).optional(),
  label: z.string().min(1).max(100).optional(),
  temperature: z.number().min(0).max(2).optional(),
  is_default_general_chat_model: z.boolean().optional(),
  status: z.enum(['active', 'inactive']).optional()
});

// GET /api/external/v1/configs/{configId}/keys - List provider keys for configuration
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Verify config belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id')
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (configError || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Get provider keys (without decrypted API keys for security)
    const { data: keys, error } = await supabase
      .from('api_keys')
      .select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        last_used_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        )
      `)
      .eq('custom_api_config_id', configId)
      .eq('user_id', userConfig!.user_id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching provider keys:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to fetch provider keys',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      object: 'list',
      data: keys || [],
      has_more: false
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in provider keys GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// POST /api/external/v1/configs/{configId}/keys - Add provider key to configuration
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId } = await params;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = CreateProviderKeySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request data',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const keyData = validationResult.data;

    // 3. Verify config belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id')
      .eq('id', configId)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (configError || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 4. Encrypt the API key
    const encryptedApiKey = await encrypt(keyData.api_key);
    
    // 5. Create API key hash for validation
    const encoder = new TextEncoder();
    const data = encoder.encode(keyData.api_key);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const apiKeyHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    // 6. Insert the provider key
    const { data: newKey, error } = await supabase
      .from('api_keys')
      .insert({
        custom_api_config_id: configId,
        user_id: userConfig!.user_id,
        provider: keyData.provider,
        predefined_model_id: keyData.model_id || null,
        encrypted_api_key: encryptedApiKey,
        label: keyData.label,
        api_key_hash: apiKeyHash,
        status: 'active',
        is_default_general_chat_model: keyData.is_default_general_chat_model,
        temperature: keyData.temperature
      })
      .select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        )
      `)
      .single();

    if (error) {
      console.error('Error creating provider key:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to create provider key',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 7. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 201,
        modelUsed: 'key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: newKey.id,
      object: 'provider_key',
      provider: newKey.provider,
      label: newKey.label,
      status: newKey.status,
      is_default_general_chat_model: newKey.is_default_general_chat_model,
      temperature: newKey.temperature,
      created_at: newKey.created_at,
      updated_at: newKey.updated_at,
      model: newKey.predefined_models
    }, { 
      status: 201,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in provider keys POST API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
