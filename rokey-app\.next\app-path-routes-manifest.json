{"/api/activity/route": "/api/activity", "/api/admin/cleanup-semantic-cache/route": "/api/admin/cleanup-semantic-cache", "/api/admin/populate-cost-tiers/route": "/api/admin/populate-cost-tiers", "/api/analytics/cache/route": "/api/analytics/cache", "/api/analytics/errors/route": "/api/analytics/errors", "/api/analytics/metadata/route": "/api/analytics/metadata", "/api/analytics/summary/route": "/api/analytics/summary", "/api/analytics/users/route": "/api/analytics/users", "/api/auth/check-pending-payment/route": "/api/auth/check-pending-payment", "/api/auth/generate-magic-link/route": "/api/auth/generate-magic-link", "/api/auth/paid-signup/route": "/api/auth/paid-signup", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/auth/free-signup/route": "/api/auth/free-signup", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/custom-configs/[configId]/browsing/route": "/api/custom-configs/[configId]/browsing", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/debug/checkout/route": "/api/debug/checkout", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/route": "/api/custom-configs", "/api/debug/auth-test/route": "/api/debug/auth-test", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/debug/env-check/route": "/api/debug/env-check", "/api/debug/subscription-data/route": "/api/debug/subscription-data", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/debug/test-webhook/route": "/api/debug/test-webhook", "/api/debug/webhook-logs/route": "/api/debug/webhook-logs", "/api/debug/workflow-tools/route": "/api/debug/workflow-tools", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/documents/list/route": "/api/documents/list", "/api/documents/search/route": "/api/documents/search", "/api/email/test-welcome/route": "/api/email/test-welcome", "/api/documents/upload/route": "/api/documents/upload", "/api/external/v1/async/result/[jobId]/route": "/api/external/v1/async/result/[jobId]", "/api/external/v1/async/status/[jobId]/route": "/api/external/v1/async/status/[jobId]", "/api/external/v1/async/submit/route": "/api/external/v1/async/submit", "/api/internal/async/process/route": "/api/internal/async/process", "/api/internal/classify-multi-role/route": "/api/internal/classify-multi-role", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/logs/route": "/api/logs", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/keys/route": "/api/keys", "/api/memory/stats/route": "/api/memory/stats", "/api/orchestration/classify-roles/route": "/api/orchestration/classify-roles", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/playground/route": "/api/playground", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/providers/list-models/route": "/api/providers/list-models", "/api/quality-analytics/route": "/api/quality-analytics", "/api/quality-feedback/route": "/api/quality-feedback", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/payment-success/route": "/api/stripe/payment-success", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/test-subscription/route": "/api/test-subscription", "/api/system-status/route": "/api/system-status", "/api/test/semantic-cache/route": "/api/test/semantic-cache", "/api/training/jobs/route": "/api/training/jobs", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/user-api-keys/[keyId]/route": "/api/user-api-keys/[keyId]", "/api/user-api-keys/route": "/api/user-api-keys", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/user/custom-roles/route": "/api/user/custom-roles", "/api/user/delete-account/route": "/api/user/delete-account", "/api/user/subscription-tier/route": "/api/user/subscription-tier", "/apple-icon.png/route": "/apple-icon.png", "/auth/callback/route": "/auth/callback", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/icon.png/route": "/icon.png", "/_not-found/page": "/_not-found", "/add-keys/page": "/add-keys", "/analytics/page": "/analytics", "/auth/recover/page": "/auth/recover", "/auth/reset-password/page": "/auth/reset-password", "/auth/signin/page": "/auth/signin", "/auth/signup/page": "/auth/signup", "/auth/verify-email/page": "/auth/verify-email", "/billing/page": "/billing", "/checkout/page": "/checkout", "/dashboard/page": "/dashboard", "/debug-session/page": "/debug-session", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/my-models/page": "/my-models", "/page": "/", "/playground/page": "/playground", "/playground/workflows/page": "/playground/workflows", "/pricing/page": "/pricing", "/routing-setup/page": "/routing-setup", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/success/page": "/success", "/test-full-browsing/page": "/test-full-browsing", "/training/page": "/training", "/(app)/settings/page": "/settings", "/about-developer/page": "/about-developer", "/blog/ai-api-gateway-2025-guide/page": "/blog/ai-api-gateway-2025-guide", "/blog/ai-model-selection-guide/page": "/blog/ai-model-selection-guide", "/blog/bootstrap-lean-startup-2025/page": "/blog/bootstrap-lean-startup-2025", "/blog/build-ai-powered-saas/page": "/blog/build-ai-powered-saas", "/blog/cost-effective-ai-development/page": "/blog/cost-effective-ai-development", "/blog/page": "/blog", "/blog/roukey-ai-routing-strategies/page": "/blog/roukey-ai-routing-strategies", "/contact/page": "/contact", "/cookies/page": "/cookies", "/features/page": "/features", "/docs/page": "/docs", "/about/page": "/about", "/routing-strategies/page": "/routing-strategies", "/security/page": "/security", "/terms/page": "/terms", "/privacy/page": "/privacy", "/api/external/v1/api-keys/route": "/api/external/v1/api-keys", "/api/external/v1/chat/completions/route": "/api/external/v1/chat/completions", "/api/external/v1/configs/[configId]/keys/[keyId]/route": "/api/external/v1/configs/[configId]/keys/[keyId]", "/api/external/v1/configs/[configId]/keys/route": "/api/external/v1/configs/[configId]/keys", "/api/external/v1/configs/[configId]/routing/route": "/api/external/v1/configs/[configId]/routing", "/api/external/v1/docs/route": "/api/external/v1/docs", "/api/external/v1/providers/route": "/api/external/v1/providers", "/api/external/v1/configs/route": "/api/external/v1/configs", "/api/external/v1/usage/route": "/api/external/v1/usage", "/api/external/v1/api-keys/[keyId]/route": "/api/external/v1/api-keys/[keyId]", "/api/external/v1/configs/[configId]/route": "/api/external/v1/configs/[configId]", "/api/external/v1/models/route": "/api/external/v1/models"}