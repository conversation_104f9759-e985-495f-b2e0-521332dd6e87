#!/usr/bin/env python3
"""
RouKey API Comprehensive Test Script (Python)
Tests all available API endpoints and functionality with your actual API key
Updated to use your provided API key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Configuration
API_KEY = 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM'
BASE_URL = 'https://roukey.online'
API_BASE = f'{BASE_URL}/api/external/v1'

# Test scenarios for chat completions
CHAT_TEST_CASES = [
    {
        'name': 'Simple greeting',
        'messages': [{'role': 'user', 'content': 'Hello! How are you?'}],
        'stream': False
    },
    {
        'name': 'Code generation request',
        'messages': [{'role': 'user', 'content': 'Write a Python function to calculate fibonacci numbers'}],
        'stream': False
    },
    {
        'name': 'Complex reasoning task',
        'messages': [{'role': 'user', 'content': 'Explain the differences between machine learning, deep learning, and artificial intelligence'}],
        'stream': False
    },
    {
        'name': 'Streaming response test',
        'messages': [{'role': 'user', 'content': 'Tell me a short story about space exploration'}],
        'stream': True
    },
    {
        'name': 'Role-based routing test',
        'messages': [{'role': 'user', 'content': 'Explain quantum computing principles'}],
        'role': 'science_expert',
        'stream': False
    },
    {
        'name': 'Agent mode complexity test',
        'messages': [{'role': 'user', 'content': 'Design a comprehensive business plan for a sustainable energy startup, including market analysis, financial projections, and technical implementation strategy'}],
        'stream': False
    },
    {
        'name': 'Temperature test (creative)',
        'messages': [{'role': 'user', 'content': 'Write a creative poem about technology'}],
        'temperature': 1.2,
        'stream': False
    },
    {
        'name': 'Temperature test (factual)',
        'messages': [{'role': 'user', 'content': 'What is the speed of light in vacuum?'}],
        'temperature': 0.1,
        'stream': False
    }
]

class RouKeyAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-Key': API_KEY,
            'User-Agent': 'RouKey-Python-Test/1.0'
        })
        
    def make_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None, description: str = '') -> Dict[str, Any]:
        """Make API request with proper error handling"""
        print(f"\n🔗 {description or method} {endpoint}")
        print('─' * 50)
        
        if data and method != 'GET':
            print(f"📤 Request Body: {json.dumps(data, indent=2)}")
        
        try:
            start_time = time.time()
            
            if method == 'GET':
                response = self.session.get(endpoint)
            elif method == 'POST':
                response = self.session.post(endpoint, json=data)
            elif method == 'PUT':
                response = self.session.put(endpoint, json=data)
            elif method == 'DELETE':
                response = self.session.delete(endpoint)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            duration = (time.time() - start_time) * 1000
            
            print(f"📊 Status: {response.status_code} {response.reason}")
            print(f"⏱️  Duration: {duration:.1f}ms")
            
            try:
                response_data = response.json()
                print(f"📥 Response: {json.dumps(response_data, indent=2)}")
            except json.JSONDecodeError:
                response_data = response.text
                print(f"📥 Raw Response: {response_data}")
            
            return {
                'success': response.ok,
                'status': response.status_code,
                'data': response_data,
                'response': response
            }
            
        except Exception as error:
            print(f"❌ Request failed: {str(error)}")
            return {'success': False, 'error': str(error)}
    
    def test_connectivity(self) -> bool:
        """Test basic connectivity to the deployment"""
        print('🔍 Testing connectivity to production deployment...')
        
        try:
            response = requests.get(BASE_URL, timeout=10)
            print(f"📊 Base URL Status: {response.status_code} {response.reason}")
            
            if response.ok or response.status_code == 404:  # 404 is fine for base URL
                print('✅ Production deployment is accessible')
                return True
            else:
                print('⚠️  Production deployment returned unexpected status')
                return False
        except Exception as error:
            print(f"❌ Cannot reach production deployment: {str(error)}")
            return False
    
    def debug_api_key(self):
        """Validate API key format and test authentication"""
        print('\n🔍 API KEY VALIDATION')
        print('=' * 60)
        print(f"🔑 API Key: {API_KEY[:20]}...{API_KEY[-4:]}")
        print(f"📏 Key Length: {len(API_KEY)}")
        print(f"🏷️  Key Format: {'✅ Valid live key prefix' if API_KEY.startswith('rk_live_') else '❌ Invalid prefix'}")
        
        # Test basic authentication
        print('\n🧪 Testing API Key Authentication...')
        result = self.make_request(f'{API_BASE}/providers', 'GET', None, 'Test Authentication')
        
        if result['success']:
            print('✅ API Key authentication successful!')
            if isinstance(result['data'], dict) and 'data' in result['data']:
                print(f"📊 Providers available: {len(result['data']['data'])}")
        else:
            print('❌ API Key authentication failed')
    
    def test_api_endpoints(self) -> List[Dict[str, Any]]:
        """Test all API endpoints"""
        print('\n🔧 TESTING ALL API ENDPOINTS')
        print('=' * 60)
        
        results = []
        
        # Test each endpoint
        endpoints = [
            ('docs', 'Get API Documentation'),
            ('providers', 'List Supported Providers'),
            ('models', 'List Available Models'),
            ('configs', 'List Configurations'),
            ('usage', 'Get Usage Analytics'),
            ('api-keys', 'List API Keys')
        ]
        
        for endpoint, description in endpoints:
            print(f'\n📚 Testing {description}...')
            result = self.make_request(f'{API_BASE}/{endpoint}', 'GET', None, description)
            results.append({'name': description, 'success': result['success']})
            time.sleep(1)  # Rate limiting
        
        return results

    def test_chat_completions(self) -> tuple[int, int]:
        """Test chat completion functionality"""
        print('\n💬 TESTING CHAT COMPLETIONS')
        print('=' * 60)

        success_count = 0
        total_tests = len(CHAT_TEST_CASES)

        for i, test_case in enumerate(CHAT_TEST_CASES, 1):
            print(f"\n🧪 Chat Test {i}: {test_case['name']}")
            print('=' * 50)

            request_body = {
                'messages': test_case['messages'],
                'stream': test_case.get('stream', False),
                'max_tokens': 500
            }

            if 'temperature' in test_case:
                request_body['temperature'] = test_case['temperature']
            if 'role' in test_case:
                request_body['role'] = test_case['role']

            result = self.make_request(f'{API_BASE}/chat/completions', 'POST', request_body, f"Chat Test {i}")

            if result['success']:
                success_count += 1

                # Check for RouKey-specific features
                if isinstance(result['data'], dict):
                    if 'rokey_metadata' in result['data']:
                        metadata = result['data']['rokey_metadata']
                        print('🎭 RouKey Metadata:')
                        if 'roles_used' in metadata:
                            print(f"   Roles used: {', '.join(metadata['roles_used'])}")
                        if 'routing_strategy' in metadata:
                            print(f"   Routing strategy: {metadata['routing_strategy']}")
                        if 'provider_used' in metadata:
                            print(f"   Provider: {metadata['provider_used']}")

                    if 'choices' in result['data'] and result['data']['choices']:
                        content = result['data']['choices'][0].get('message', {}).get('content', '')
                        print(f"📏 Response length: {len(content)} characters")

                print('✅ Request successful')
            else:
                print('❌ Request failed')

            # Wait between requests
            if i < total_tests:
                print('\n⏳ Waiting 3 seconds before next test...')
                time.sleep(3)

        return success_count, total_tests

    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print('🚀 STARTING COMPREHENSIVE ROKEY API TESTS')
        print('=' * 60)
        print(f"🔑 API Key: {API_KEY[:20]}...{API_KEY[-4:]}")
        print(f"🌐 Base URL: {BASE_URL}")
        print(f"📡 API Base: {API_BASE}")
        print(f"📅 Started at: {datetime.now().isoformat()}")

        # Test connectivity
        if not self.test_connectivity():
            print('❌ Cannot proceed with tests - deployment not accessible')
            return

        # Debug API key
        self.debug_api_key()

        print('\n⏳ Waiting 2 seconds before starting comprehensive tests...')
        time.sleep(2)

        # Test API endpoints
        endpoint_results = self.test_api_endpoints()

        print('\n⏳ Waiting 3 seconds before chat completion tests...')
        time.sleep(3)

        # Test chat completions
        chat_success_count, total_chat_tests = self.test_chat_completions()

        # Final summary
        print('\n' + '=' * 60)
        print('📊 COMPREHENSIVE TEST SUMMARY')
        print('=' * 60)

        print('\n🔧 API Endpoints Results:')
        for result in endpoint_results:
            print(f"   {'✅' if result['success'] else '❌'} {result['name']}")

        endpoint_success_count = sum(1 for r in endpoint_results if r['success'])

        print('\n📈 SUMMARY BY CATEGORY:')
        print(f"📡 API Endpoints: {endpoint_success_count}/{len(endpoint_results)} ({(endpoint_success_count/len(endpoint_results)*100):.1f}%)")
        print(f"💬 Chat Completions: {chat_success_count}/{total_chat_tests} ({(chat_success_count/total_chat_tests*100):.1f}%)")

        total_tests = len(endpoint_results) + total_chat_tests
        total_successful = endpoint_success_count + chat_success_count

        print(f"\n🎯 OVERALL SUCCESS RATE: {(total_successful / total_tests * 100):.1f}% ({total_successful}/{total_tests})")
        print(f"📅 Completed at: {datetime.now().isoformat()}")

        if total_successful == total_tests:
            print('\n🎉 ALL TESTS PASSED! Your RouKey API is working perfectly!')
            print('✨ Your API key has full access to all RouKey features.')
            print('🚀 You can now confidently integrate RouKey into your applications!')
        elif total_successful / total_tests >= 0.8:
            print('\n✅ Most tests passed! Your RouKey API is working well.')
            print('⚠️  Some minor issues detected - check logs for details.')
        else:
            print('\n⚠️  Several tests failed. Check the logs above for details.')
            print('💡 This might indicate configuration issues or missing permissions.')
            print('📞 Contact <NAME_EMAIL> if issues persist.')

def main():
    """Main function to run the tests"""
    try:
        tester = RouKeyAPITester()
        tester.run_comprehensive_tests()
    except KeyboardInterrupt:
        print('\n\n⚠️  Tests interrupted by user')
    except Exception as error:
        print(f'\n❌ Fatal error: {str(error)}')
        raise

if __name__ == '__main__':
    main()
