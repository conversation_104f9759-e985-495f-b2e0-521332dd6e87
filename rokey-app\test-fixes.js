#!/usr/bin/env node

/**
 * Test script for the fixed RouKey API endpoints
 * Tests the specific fixes for models, configs, and usage endpoints
 */

const API_KEY = 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM';
const BASE_URL = 'https://roukey.online';
const API_BASE = `${BASE_URL}/api/external/v1`;

// Helper function to make API requests
async function testApiRequest(endpoint, method = 'GET', body = null, description = '') {
  console.log(`\n🧪 Testing: ${description || method} ${endpoint}`);
  console.log('─'.repeat(50));
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
      'User-Agent': 'RouKey-Fix-Test/1.0'
    }
  };

  if (body && method !== 'GET') {
    options.body = JSON.stringify(body);
    console.log('📤 Request Body:', JSON.stringify(body, null, 2));
  }

  try {
    const startTime = Date.now();
    const response = await fetch(endpoint, options);
    const duration = Date.now() - startTime;

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Duration: ${duration}ms`);

    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
      console.log('📥 Response:', JSON.stringify(responseData, null, 2));
    } catch {
      console.log('📥 Raw Response:', responseText);
      responseData = responseText;
    }

    return { success: response.ok, status: response.status, data: responseData, response };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { success: false, error: error.message };
  }
}

async function testFixedEndpoints() {
  console.log('🚀 TESTING FIXED ROKEY API ENDPOINTS');
  console.log('='.repeat(60));
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);

  const results = [];

  // Test 1: Fixed Models endpoint
  console.log('\n🤖 TESTING FIXED MODELS ENDPOINT');
  console.log('='.repeat(40));
  const modelsResult = await testApiRequest(`${API_BASE}/models`, 'GET', null, 'List Available Models (Fixed)');
  results.push({ name: 'Models Endpoint (Fixed)', success: modelsResult.success, status: modelsResult.status });

  // Test 2: Fixed Configs endpoint
  console.log('\n⚙️  TESTING FIXED CONFIGS ENDPOINT');
  console.log('='.repeat(40));
  const configsResult = await testApiRequest(`${API_BASE}/configs`, 'GET', null, 'List Configurations (Fixed)');
  results.push({ name: 'Configs Endpoint (Fixed)', success: configsResult.success, status: configsResult.status });

  // Test 3: Fixed Usage endpoint (no parameters)
  console.log('\n📊 TESTING FIXED USAGE ENDPOINT (No Parameters)');
  console.log('='.repeat(40));
  const usageResult = await testApiRequest(`${API_BASE}/usage`, 'GET', null, 'Get Usage Analytics (Fixed - No Params)');
  results.push({ name: 'Usage Endpoint (No Params)', success: usageResult.success, status: usageResult.status });

  // Test 4: Fixed Usage endpoint (with parameters)
  console.log('\n📊 TESTING FIXED USAGE ENDPOINT (With Parameters)');
  console.log('='.repeat(40));
  const usageParams = new URLSearchParams({
    granularity: 'day',
    start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    end_date: new Date().toISOString()
  });
  const usageParamsResult = await testApiRequest(`${API_BASE}/usage?${usageParams}`, 'GET', null, 'Get Usage Analytics (Fixed - With Params)');
  results.push({ name: 'Usage Endpoint (With Params)', success: usageParamsResult.success, status: usageParamsResult.status });

  // Test 5: Verify chat completions still work
  console.log('\n💬 VERIFYING CHAT COMPLETIONS STILL WORK');
  console.log('='.repeat(40));
  const chatTest = {
    messages: [{ role: 'user', content: 'Hello! Please respond with "API test successful"' }],
    max_tokens: 50
  };
  const chatResult = await testApiRequest(`${API_BASE}/chat/completions`, 'POST', chatTest, 'Chat Completion Verification');
  results.push({ name: 'Chat Completions', success: chatResult.success, status: chatResult.status });

  // Test 6: Verify providers endpoint still works
  console.log('\n🏢 VERIFYING PROVIDERS ENDPOINT STILL WORKS');
  console.log('='.repeat(40));
  const providersResult = await testApiRequest(`${API_BASE}/providers`, 'GET', null, 'List Providers Verification');
  results.push({ name: 'Providers Endpoint', success: providersResult.success, status: providersResult.status });

  // Summary
  console.log('\n📋 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  let successCount = 0;
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name} (Status: ${result.status})`);
    if (result.success) successCount++;
  });

  const successRate = (successCount / results.length) * 100;
  console.log(`\n🎯 Overall Success Rate: ${successRate.toFixed(1)}% (${successCount}/${results.length})`);
  console.log(`📅 Completed at: ${new Date().toISOString()}`);

  if (successCount === results.length) {
    console.log('\n🎉 ALL FIXES SUCCESSFUL! All endpoints are now working correctly.');
  } else if (successRate >= 80) {
    console.log('\n✅ Most fixes successful! Significant improvement achieved.');
  } else {
    console.log('\n⚠️  Some fixes still need work. Check the logs above for details.');
  }

  // Detailed analysis for any remaining failures
  const failures = results.filter(r => !r.success);
  if (failures.length > 0) {
    console.log('\n🔍 REMAINING ISSUES:');
    failures.forEach(failure => {
      console.log(`   ❌ ${failure.name}: Status ${failure.status}`);
    });
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
testFixedEndpoints().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
