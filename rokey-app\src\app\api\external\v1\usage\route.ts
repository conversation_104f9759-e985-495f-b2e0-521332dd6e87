import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

// Validation schema for usage query parameters
const UsageQuerySchema = z.object({
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  granularity: z.enum(['hour', 'day', 'week', 'month']).default('day'),
  config_id: z.string().uuid().optional(),
  provider: z.string().optional(),
  model: z.string().optional()
});

// GET /api/external/v1/usage - Get usage statistics and analytics
export async function GET(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      start_date: searchParams.get('start_date') || undefined,
      end_date: searchParams.get('end_date') || undefined,
      granularity: searchParams.get('granularity') || 'day',
      config_id: searchParams.get('config_id') || undefined,
      provider: searchParams.get('provider') || undefined,
      model: searchParams.get('model') || undefined
    };

    const validationResult = UsageQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid query parameters',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const { start_date, end_date, granularity, config_id, provider, model } = validationResult.data;

    // 3. Set default date range if not provided (last 30 days)
    const endDate = end_date ? new Date(end_date) : new Date();
    const startDate = start_date ? new Date(start_date) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const supabase = createSupabaseServerClientFromRequest(request);

    // 4. Get user's API key usage logs
    let query = supabase
      .from('user_api_key_usage_logs')
      .select(`
        id,
        created_at,
        status_code,
        model_used,
        provider_used,
        request_tokens,
        response_tokens,
        total_tokens,
        cost_usd,
        user_generated_api_keys!inner(
          id,
          key_name,
          custom_api_config_id,
          custom_api_configs!inner(
            id,
            name,
            user_id
          )
        )
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });

    // Filter by user's API keys only
    query = query.eq('user_generated_api_keys.custom_api_configs.user_id', userConfig!.user_id);

    // Apply additional filters
    if (config_id) {
      query = query.eq('user_generated_api_keys.custom_api_config_id', config_id);
    }
    if (provider) {
      query = query.eq('provider_used', provider);
    }
    if (model) {
      query = query.eq('model_used', model);
    }

    const { data: usageLogs, error } = await query;

    if (error) {
      console.error('Error fetching usage logs:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to fetch usage data',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 5. Process and aggregate usage data
    const logs = usageLogs || [];
    
    // Calculate totals
    const totals = logs.reduce((acc, log) => ({
      total_requests: acc.total_requests + 1,
      total_tokens: acc.total_tokens + (log.total_tokens || 0),
      total_cost: acc.total_cost + (log.cost_usd || 0),
      successful_requests: acc.successful_requests + (log.status_code === 200 ? 1 : 0),
      failed_requests: acc.failed_requests + (log.status_code !== 200 ? 1 : 0)
    }), {
      total_requests: 0,
      total_tokens: 0,
      total_cost: 0,
      successful_requests: 0,
      failed_requests: 0
    });

    // Group by time period for time series data
    const timeSeriesData = logs.reduce((acc: any, log) => {
      const date = new Date(log.created_at);
      let timeKey: string;

      switch (granularity) {
        case 'hour':
          timeKey = date.toISOString().slice(0, 13) + ':00:00.000Z';
          break;
        case 'day':
          timeKey = date.toISOString().slice(0, 10) + 'T00:00:00.000Z';
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          timeKey = weekStart.toISOString().slice(0, 10) + 'T00:00:00.000Z';
          break;
        case 'month':
          timeKey = date.toISOString().slice(0, 7) + '-01T00:00:00.000Z';
          break;
        default:
          timeKey = date.toISOString().slice(0, 10) + 'T00:00:00.000Z';
      }

      if (!acc[timeKey]) {
        acc[timeKey] = {
          timestamp: timeKey,
          requests: 0,
          tokens: 0,
          cost: 0,
          success_rate: 0,
          successful: 0,
          failed: 0
        };
      }

      acc[timeKey].requests += 1;
      acc[timeKey].tokens += log.total_tokens || 0;
      acc[timeKey].cost += log.cost_usd || 0;
      
      if (log.status_code === 200) {
        acc[timeKey].successful += 1;
      } else {
        acc[timeKey].failed += 1;
      }

      acc[timeKey].success_rate = acc[timeKey].requests > 0 
        ? (acc[timeKey].successful / acc[timeKey].requests) * 100 
        : 0;

      return acc;
    }, {});

    // Convert to array and sort by timestamp
    const timeSeries = Object.values(timeSeriesData).sort((a: any, b: any) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // Group by provider and model for breakdown
    const providerBreakdown = logs.reduce((acc: any, log) => {
      const provider = log.provider_used || 'unknown';
      if (!acc[provider]) {
        acc[provider] = {
          provider: provider,
          requests: 0,
          tokens: 0,
          cost: 0
        };
      }
      acc[provider].requests += 1;
      acc[provider].tokens += log.total_tokens || 0;
      acc[provider].cost += log.cost_usd || 0;
      return acc;
    }, {});

    const modelBreakdown = logs.reduce((acc: any, log) => {
      const model = log.model_used || 'unknown';
      if (!acc[model]) {
        acc[model] = {
          model: model,
          requests: 0,
          tokens: 0,
          cost: 0
        };
      }
      acc[model].requests += 1;
      acc[model].tokens += log.total_tokens || 0;
      acc[model].cost += log.cost_usd || 0;
      return acc;
    }, {});

    // 6. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'usage_analytics',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      object: 'usage_report',
      period: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        granularity: granularity
      },
      totals: {
        ...totals,
        success_rate: totals.total_requests > 0 
          ? (totals.successful_requests / totals.total_requests) * 100 
          : 0
      },
      time_series: timeSeries,
      breakdowns: {
        by_provider: Object.values(providerBreakdown),
        by_model: Object.values(modelBreakdown)
      },
      filters_applied: {
        config_id,
        provider,
        model
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'X-RouKey-Usage-Period': `${startDate.toISOString()} to ${endDate.toISOString()}`,
        'X-RouKey-Total-Requests': totals.total_requests.toString(),
      }
    });

  } catch (error) {
    console.error('Error in usage GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
