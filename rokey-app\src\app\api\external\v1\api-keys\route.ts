import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { Api<PERSON>eyGenerator } from '@/lib/userApiKeys/apiKeyGenerator';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

// Validation schema for creating API keys
const CreateApiKeySchema = z.object({
  config_id: z.string().uuid(),
  key_name: z.string().min(1).max(100),
  expires_at: z.string().datetime().optional(),
  permissions: z.object({
    chat: z.boolean().optional().default(true),
    streaming: z.boolean().optional().default(true),
    all_models: z.boolean().optional().default(true)
  }).optional().default({
    chat: true,
    streaming: true,
    all_models: true
  }),
  allowed_ips: z.array(z.string().ip()).optional().default([]),
  allowed_domains: z.array(z.string()).optional().default([])
});

const UpdateApiKeySchema = z.object({
  key_name: z.string().min(1).max(100).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  expires_at: z.string().datetime().nullable().optional(),
  permissions: z.object({
    chat: z.boolean().optional(),
    streaming: z.boolean().optional(),
    all_models: z.boolean().optional()
  }).optional(),
  allowed_ips: z.array(z.string().ip()).optional(),
  allowed_domains: z.array(z.string()).optional()
});

// GET /api/external/v1/api-keys - List user's generated API keys
export async function GET(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Get query parameters
    const { searchParams } = new URL(request.url);
    const configId = searchParams.get('config_id');
    const status = searchParams.get('status');

    // 3. Build query
    const supabase = createSupabaseServerClientFromRequest(request);
    
    let query = supabase
      .from('user_generated_api_keys')
      .select(`
        id,
        key_name,
        key_prefix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name,
          user_id
        )
      `)
      .eq('custom_api_configs.user_id', userConfig!.user_id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (configId) {
      query = query.eq('custom_api_config_id', configId);
    }
    if (status) {
      query = query.eq('status', status);
    }

    const { data: apiKeys, error } = await query;

    if (error) {
      console.error('Error fetching API keys:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to fetch API keys',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 4. Transform data for response (mask keys for security)
    const transformedKeys = (apiKeys || []).map(key => ({
      id: key.id,
      object: 'api_key',
      key_name: key.key_name,
      key_prefix: key.key_prefix,
      masked_key: `${key.key_prefix}_${'*'.repeat(28)}xxxx`,
      permissions: key.permissions,
      allowed_ips: key.allowed_ips,
      allowed_domains: key.allowed_domains,
      total_requests: key.total_requests,
      last_used_at: key.last_used_at,
      status: key.status,
      expires_at: key.expires_at,
      created_at: key.created_at,
      updated_at: key.updated_at,
      config: {
        id: (key.custom_api_configs as any).id,
        name: (key.custom_api_configs as any).name
      }
    }));

    // 5. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'api_key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      object: 'list',
      data: transformedKeys,
      has_more: false,
      total_count: transformedKeys.length
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in API keys GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// POST /api/external/v1/api-keys - Create new API key
export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = CreateApiKeySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request data',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const keyData = validationResult.data;

    // 3. Verify config belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, name')
      .eq('id', keyData.config_id)
      .eq('user_id', userConfig!.user_id)
      .single();

    if (configError || !config) {
      return NextResponse.json(
        {
          error: {
            message: 'Configuration not found',
            type: 'not_found_error',
            code: 'config_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 4. Generate the API key
    const { fullKey, prefix, secretPart, hash } = await ApiKeyGenerator.generateApiKey();
    const encryptedSuffix = await ApiKeyGenerator.encryptSuffix(secretPart);

    // 5. Create API key record
    const { data: newApiKey, error: createError } = await supabase
      .from('user_generated_api_keys')
      .insert({
        user_id: userConfig!.user_id,
        custom_api_config_id: keyData.config_id,
        key_name: keyData.key_name,
        key_prefix: prefix,
        key_hash: hash,
        encrypted_key_suffix: encryptedSuffix,
        permissions: keyData.permissions,
        allowed_ips: keyData.allowed_ips,
        allowed_domains: keyData.allowed_domains,
        expires_at: keyData.expires_at || null
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating API key:', createError);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to create API key',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 6. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 201,
        modelUsed: 'api_key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: newApiKey.id,
      object: 'api_key',
      key_name: newApiKey.key_name,
      api_key: fullKey, // Full key - only shown once!
      key_prefix: newApiKey.key_prefix,
      permissions: newApiKey.permissions,
      allowed_ips: newApiKey.allowed_ips,
      allowed_domains: newApiKey.allowed_domains,
      status: newApiKey.status,
      expires_at: newApiKey.expires_at,
      created_at: newApiKey.created_at,
      config: {
        id: config.id,
        name: config.name
      },
      warning: 'Save this API key now - this is the only time you will see it in full!'
    }, { 
      status: 201,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in API keys POST API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
