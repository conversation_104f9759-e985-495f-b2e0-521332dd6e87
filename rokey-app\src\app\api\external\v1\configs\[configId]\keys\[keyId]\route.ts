import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { encrypt } from '@/lib/encryption';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

interface RouteParams {
  params: Promise<{
    configId: string;
    keyId: string;
  }>;
}

// Validation schema for updating provider keys
const UpdateProviderKeySchema = z.object({
  api_key: z.string().min(1).optional(),
  label: z.string().min(1).max(100).optional(),
  temperature: z.number().min(0).max(2).optional(),
  is_default_general_chat_model: z.boolean().optional(),
  status: z.enum(['active', 'inactive']).optional()
});

// GET /api/external/v1/configs/{configId}/keys/{keyId} - Get specific provider key
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId, keyId } = await params;

    // 2. Get provider key
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: key, error } = await supabase
      .from('api_keys')
      .select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        last_used_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        ),
        custom_api_configs!inner(
          id,
          user_id
        )
      `)
      .eq('id', keyId)
      .eq('custom_api_config_id', configId)
      .single();

    if (error || !key || (key.custom_api_configs as any).user_id !== userConfig!.user_id) {
      return NextResponse.json(
        {
          error: {
            message: 'Provider key not found',
            type: 'not_found_error',
            code: 'key_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: key.id,
      object: 'provider_key',
      provider: key.provider,
      label: key.label,
      status: key.status,
      is_default_general_chat_model: key.is_default_general_chat_model,
      temperature: key.temperature,
      created_at: key.created_at,
      updated_at: key.updated_at,
      last_used_at: key.last_used_at,
      model: key.predefined_models
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in provider key GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// PUT /api/external/v1/configs/{configId}/keys/{keyId} - Update provider key
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId, keyId } = await params;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = UpdateProviderKeySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request data',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // 3. Prepare update object
    const supabase = createSupabaseServerClientFromRequest(request);
    let updateObject: any = {
      ...updateData,
      updated_at: new Date().toISOString()
    };

    // If updating API key, encrypt it
    if (updateData.api_key) {
      const encryptedApiKey = await encrypt(updateData.api_key);
      
      // Create new API key hash
      const encoder = new TextEncoder();
      const data = encoder.encode(updateData.api_key);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const apiKeyHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

      updateObject.encrypted_api_key = encryptedApiKey;
      updateObject.api_key_hash = apiKeyHash;
      delete updateObject.api_key; // Remove plaintext key
    }

    // 4. Update provider key
    const { data: updatedKey, error } = await supabase
      .from('api_keys')
      .update(updateObject)
      .eq('id', keyId)
      .eq('custom_api_config_id', configId)
      .eq('user_id', userConfig!.user_id)
      .select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        )
      `)
      .single();

    if (error || !updatedKey) {
      return NextResponse.json(
        {
          error: {
            message: 'Provider key not found or failed to update',
            type: 'not_found_error',
            code: 'key_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 5. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: updatedKey.id,
      object: 'provider_key',
      provider: updatedKey.provider,
      label: updatedKey.label,
      status: updatedKey.status,
      is_default_general_chat_model: updatedKey.is_default_general_chat_model,
      temperature: updatedKey.temperature,
      created_at: updatedKey.created_at,
      updated_at: updatedKey.updated_at,
      model: updatedKey.predefined_models
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in provider key PUT API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// DELETE /api/external/v1/configs/{configId}/keys/{keyId} - Delete provider key
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { configId, keyId } = await params;

    // 2. Verify key exists and belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: key, error: fetchError } = await supabase
      .from('api_keys')
      .select(`
        id,
        label,
        custom_api_configs!inner(
          user_id
        )
      `)
      .eq('id', keyId)
      .eq('custom_api_config_id', configId)
      .single();

    if (fetchError || !key || (key.custom_api_configs as any).user_id !== userConfig!.user_id) {
      return NextResponse.json(
        {
          error: {
            message: 'Provider key not found',
            type: 'not_found_error',
            code: 'key_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Delete provider key
    const { error: deleteError } = await supabase
      .from('api_keys')
      .delete()
      .eq('id', keyId)
      .eq('custom_api_config_id', configId)
      .eq('user_id', userConfig!.user_id);

    if (deleteError) {
      console.error('Error deleting provider key:', deleteError);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to delete provider key',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: keyId,
      object: 'provider_key',
      deleted: true
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in provider key DELETE API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
