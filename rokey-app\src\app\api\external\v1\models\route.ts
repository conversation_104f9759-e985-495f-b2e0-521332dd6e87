import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

// GET /api/external/v1/models - List all available models (OpenAI-compatible)
export async function GET(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Get all available models
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: models, error } = await supabase
      .from('models')
      .select(`
        id,
        name,
        display_name,
        description,
        provider_id,
        family,
        context_window,
        input_token_limit,
        output_token_limit,
        modality,
        is_public
      `)
      .eq('is_public', true)
      .order('provider_id', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching models:', error);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to fetch models',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 3. Transform to OpenAI-compatible format
    const openaiCompatibleModels = (models || []).map(model => ({
      id: model.name, // Use model name as ID for OpenAI compatibility
      object: 'model',
      created: Math.floor(new Date().getTime() / 1000), // Unix timestamp
      owned_by: model.provider_id,
      permission: [
        {
          id: `modelperm-${model.id}`,
          object: 'model_permission',
          created: Math.floor(new Date().getTime() / 1000),
          allow_create_engine: false,
          allow_sampling: true,
          allow_logprobs: true,
          allow_search_indices: false,
          allow_view: true,
          allow_fine_tuning: false,
          organization: '*',
          group: null,
          is_blocking: false
        }
      ],
      // RouKey-specific extensions
      rokey_extensions: {
        display_name: model.display_name,
        description: model.description,
        family: model.family,
        context_window: model.context_window,
        input_token_limit: model.input_token_limit,
        output_token_limit: model.output_token_limit,
        modality: model.modality,
        provider: model.provider_id
      }
    }));

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'model_listing',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      object: 'list',
      data: openaiCompatibleModels
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'X-RouKey-Total-Models': openaiCompatibleModels.length.toString(),
      }
    });

  } catch (error) {
    console.error('Error in models GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
