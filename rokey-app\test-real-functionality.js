#!/usr/bin/env node

/**
 * RouKey Real Functionality Test Script
 * Tests the core features that actually matter:
 * - Multi-role routing with complex tasks
 * - Knowledge base integration
 * - Intelligent routing strategies
 * - Agent mode collaboration
 */

const API_KEY = 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM';
const BASE_URL = 'https://roukey.online';
const API_BASE = `${BASE_URL}/api/external/v1`;

// Real-world test scenarios that matter
const REAL_TEST_SCENARIOS = [
  {
    name: 'Multi-Role Snake Game Development',
    description: 'Complex task requiring creative brainstorming + coding expertise',
    messages: [{
      role: 'user',
      content: 'Brainstorm a novel snake game idea and write Python code for it. Make it unique and innovative with modern features.'
    }],
    stream: true,
    expectedRoles: ['creative_writer', 'code_expert', 'game_designer'],
    expectedStrategy: 'intelligent_role'
  },
  {
    name: 'Knowledge Base Test - LLM Router',
    description: 'Test knowledge base integration for LLM Router information',
    messages: [{
      role: 'user',
      content: 'What is LLM Router and did it get verified? Tell me everything you know about it.'
    }],
    stream: true,
    expectedKnowledgeBase: true
  },
  {
    name: 'Knowledge Base Test - Riftshot',
    description: 'Test knowledge base integration for Riftshot information',
    messages: [{
      role: 'user',
      content: 'What is Riftshot and who created it? Provide detailed information about this project.'
    }],
    stream: true,
    expectedKnowledgeBase: true
  },
  {
    name: 'Personal Knowledge Test - Okoro David',
    description: 'Test knowledge base for personal information',
    messages: [{
      role: 'user',
      content: 'Who is Okoro David? Tell me about his background, projects, and achievements.'
    }],
    stream: true,
    expectedKnowledgeBase: true
  },
  {
    name: 'Complex Multi-Role Business Analysis',
    description: 'Advanced multi-role task requiring business analysis + technical expertise',
    messages: [{
      role: 'user',
      content: 'Analyze the competitive landscape for AI routing platforms like RouKey, identify key differentiators, and propose a technical architecture for scaling to 1M+ requests per day.'
    }],
    stream: true,
    expectedRoles: ['business_analyst', 'technical_architect', 'market_researcher'],
    expectedStrategy: 'intelligent_role'
  },
  {
    name: 'Agent Mode Complexity Test',
    description: 'High complexity task that should trigger agent mode collaboration',
    messages: [{
      role: 'user',
      content: 'Design a comprehensive AI-powered educational platform that includes personalized learning paths, real-time assessment, gamification elements, and integration with existing LMS systems. Include technical specifications, business model, market analysis, user experience design, and implementation roadmap.'
    }],
    stream: true,
    expectedStrategy: 'agent_mode',
    expectedComplexity: 4
  },
  {
    name: 'Streaming Multi-Role Creative Task',
    description: 'Creative writing + technical analysis with streaming',
    messages: [{ 
      role: 'user', 
      content: 'Write a compelling science fiction story about AI consciousness, then analyze the technical feasibility of the AI concepts presented in the story.' 
    }],
    stream: true,
    expectedRoles: ['creative_writer', 'technical_analyst'],
    expectedStrategy: 'intelligent_role'
  },
  {
    name: 'RouKey Knowledge Test',
    description: 'Test knowledge about RouKey itself',
    messages: [{
      role: 'user',
      content: 'What is RouKey? How does it work and what makes it different from other AI routing solutions?'
    }],
    stream: true,
    expectedKnowledgeBase: true
  }
];

// Helper function to make chat completion requests with detailed analysis
async function testRealScenario(scenario, testNumber) {
  console.log(`\n🎯 Test ${testNumber}: ${scenario.name}`);
  console.log('=' .repeat(70));
  console.log(`📝 Description: ${scenario.description}`);
  console.log(`💭 Prompt: "${scenario.messages[0].content.substring(0, 100)}..."`);
  
  const requestBody = {
    messages: scenario.messages,
    stream: scenario.stream || false,
    max_tokens: 2000, // Increased for complex responses
    temperature: 0.7
  };

  console.log('📤 Request Details:');
  console.log(`   Stream: ${requestBody.stream}`);
  console.log(`   Max Tokens: ${requestBody.max_tokens}`);
  console.log(`   Temperature: ${requestBody.temperature}`);

  try {
    const startTime = Date.now();

    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Real-Test/1.0'
      },
      body: JSON.stringify(requestBody)
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Duration: ${duration}ms`);

    if (scenario.stream) {
      console.log('📡 Streaming Response:');
      console.log('─'.repeat(50));
      
      if (response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        let chunkCount = 0;
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            process.stdout.write(chunk);
            fullResponse += chunk;
            chunkCount++;
          }
        } catch (streamError) {
          console.error('❌ Streaming error:', streamError.message);
        }
        
        console.log('\n─'.repeat(50));
        console.log(`📏 Total response length: ${fullResponse.length} characters`);
        console.log(`📦 Chunks received: ${chunkCount}`);
      }
    } else {
      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
        
        // Analyze the response content
        if (responseData.choices && responseData.choices[0]) {
          const content = responseData.choices[0].message?.content || '';
          console.log(`📏 Response length: ${content.length} characters`);
          
          // Show first 200 characters of response
          console.log('📥 Response Preview:');
          console.log(`"${content.substring(0, 200)}..."`);
        }

        // Analyze RouKey metadata
        if (responseData.rokey_metadata) {
          console.log('\n🎭 RouKey Intelligence Analysis:');
          const metadata = responseData.rokey_metadata;
          
          if (metadata.routing_strategy) {
            console.log(`   🧠 Routing Strategy: ${metadata.routing_strategy}`);
            
            // Check if expected strategy matches
            if (scenario.expectedStrategy && metadata.routing_strategy === scenario.expectedStrategy) {
              console.log(`   ✅ Expected strategy "${scenario.expectedStrategy}" detected!`);
            } else if (scenario.expectedStrategy) {
              console.log(`   ⚠️  Expected "${scenario.expectedStrategy}" but got "${metadata.routing_strategy}"`);
            }
          }
          
          if (metadata.roles_used && metadata.roles_used.length > 0) {
            console.log(`   🎭 Roles Used: ${metadata.roles_used.join(', ')}`);
            
            // Check for multi-role detection
            if (metadata.roles_used.length > 1) {
              console.log(`   🚀 Multi-role routing detected! (${metadata.roles_used.length} roles)`);
            }
            
            // Check expected roles
            if (scenario.expectedRoles) {
              const foundExpectedRoles = scenario.expectedRoles.filter(role => 
                metadata.roles_used.includes(role)
              );
              if (foundExpectedRoles.length > 0) {
                console.log(`   ✅ Expected roles found: ${foundExpectedRoles.join(', ')}`);
              }
            }
          }
          
          if (metadata.provider_used) {
            console.log(`   🏢 Provider: ${metadata.provider_used}`);
          }
          
          if (metadata.config_name) {
            console.log(`   ⚙️  Config: ${metadata.config_name}`);
          }
          
          if (metadata.api_key_name) {
            console.log(`   🔑 API Key: ${metadata.api_key_name}`);
          }
          
          // Check for knowledge base usage
          if (scenario.expectedKnowledgeBase) {
            console.log('   📚 Knowledge Base Expected: YES');
            // Look for indicators of knowledge base usage in the response
            const content = responseData.choices[0]?.message?.content || '';
            const hasSpecificInfo = content.length > 500 && (
              content.includes('LLM Router') || 
              content.includes('Riftshot') || 
              content.includes('Okoro David') ||
              content.includes('RouKey')
            );
            if (hasSpecificInfo) {
              console.log('   ✅ Knowledge base appears to be working (specific information found)');
            } else {
              console.log('   ⚠️  Knowledge base usage unclear (generic response)');
            }
          }
          
          // Check for agent mode complexity
          if (scenario.expectedComplexity) {
            console.log(`   🤖 Expected Complexity: ${scenario.expectedComplexity}`);
            if (metadata.routing_strategy === 'agent_mode') {
              console.log('   ✅ Agent mode activated for high complexity task!');
            }
          }
        }

        // Check for async processing suggestions
        if (responseData.error && responseData.error.code === 'require_async_processing') {
          console.log('\n🤖 ASYNC PROCESSING REQUIRED:');
          console.log(`   💡 Reason: ${responseData.error.suggestion.reason}`);
          console.log(`   🔗 Async URL: ${responseData.error.suggestion.async_submit_url}`);
          console.log('   ✅ This indicates advanced multi-role/agent detection is working!');
        } else if (responseData.error && responseData.error.code === 'suggest_async_processing') {
          console.log('\n⚠️  ASYNC PROCESSING SUGGESTED:');
          console.log(`   💡 Reason: ${responseData.error.suggestion.reason}`);
        }

      } catch (parseError) {
        console.log('📥 Raw Response (not JSON):', responseText.substring(0, 500));
      }
    }

    if (!response.ok) {
      console.log('❌ Request failed');
      return false;
    }

    console.log('✅ Request successful');
    return true;

  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

async function runRealFunctionalityTests() {
  console.log('🚀 TESTING REAL ROKEY FUNCTIONALITY');
  console.log('='.repeat(70));
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);
  console.log(`🌐 Domain: ${BASE_URL}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  console.log('\n🎯 Focus Areas:');
  console.log('   • Multi-role intelligent routing');
  console.log('   • Knowledge base integration');
  console.log('   • Agent mode collaboration');
  console.log('   • Complex task handling');
  console.log('   • Streaming responses');

  let successCount = 0;
  let totalTests = REAL_TEST_SCENARIOS.length;

  for (let i = 0; i < REAL_TEST_SCENARIOS.length; i++) {
    const success = await testRealScenario(REAL_TEST_SCENARIOS[i], i + 1);
    if (success) successCount++;
    
    // Wait between requests to avoid rate limiting and allow for processing
    if (i < REAL_TEST_SCENARIOS.length - 1) {
      console.log('\n⏳ Waiting 5 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }

  // Final summary
  console.log('\n' + '='.repeat(70));
  console.log('📊 REAL FUNCTIONALITY TEST SUMMARY');
  console.log('='.repeat(70));
  console.log(`✅ Successful: ${successCount}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - successCount}/${totalTests}`);
  console.log(`📈 Success Rate: ${((successCount / totalTests) * 100).toFixed(1)}%`);
  console.log(`📅 Completed at: ${new Date().toISOString()}`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 ALL REAL FUNCTIONALITY TESTS PASSED!');
    console.log('✨ Your RouKey API is performing excellently on real-world tasks!');
    console.log('🚀 Multi-role routing, knowledge base, and agent mode are working!');
  } else if (successCount / totalTests >= 0.8) {
    console.log('\n✅ Most real functionality tests passed!');
    console.log('🎯 Your RouKey API is handling complex tasks well.');
  } else {
    console.log('\n⚠️  Some real functionality tests failed.');
    console.log('💡 Check the detailed logs above for specific issues.');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the real functionality tests
runRealFunctionalityTests().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
