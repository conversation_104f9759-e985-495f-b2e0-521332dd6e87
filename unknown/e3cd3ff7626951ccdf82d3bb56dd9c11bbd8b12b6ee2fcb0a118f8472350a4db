{"version": 3, "file": "app/api/external/v1/api-keys/[keyId]/route.js", "mappings": "qFAAA,wDCAA,mGC6CO,SAASA,EAAsCC,CAAoB,EACxE,MAAOC,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CACvBC,0CAAoC,CACpCA,kNAAyC,CACzC,CACEC,QAAS,CACPC,IAAIC,GACKL,CADO,CACCG,OAAO,CAACC,GAAG,CAACC,IAAOC,MAEpCC,IAAIF,CAAY,CAAEC,CAAa,CAAEE,CAAsB,EAGvD,EACAC,OAAOJ,CAAY,CAAEG,CAAsB,EAG3C,CACF,CACF,EAEJ,2FGjEA,2TFMO,IAAME,EAAU,OAAO,EAEP,IAAIC,EAAAA,CAAoBA,CASzCC,EAAqBC,EAAAA,CAAAA,CAAAA,IATPC,EASe,CAAC,CAClCC,SAAUF,EAAAA,CAAAA,CAAAA,MAAQ,GAAGG,GAAG,CAAC,GAAGC,GAAG,CAAC,KAAKC,QAAQ,GAC7CC,OAAQN,EAAAA,CAAAA,CAAAA,IAAM,CAAC,CAAC,SAAU,WAAW,EAAEK,QAAQ,GAC/CE,WAAYP,EAAAA,CAAAA,CAAAA,MAAQ,GAAGQ,QAAQ,GAAGC,QAAQ,GAAGJ,QAAQ,GACrDK,YAAaV,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACpBW,KAAMX,EAAAA,CAAAA,CAAAA,OAAS,GAAGK,QAAQ,GAC1BO,UAAWZ,EAAAA,CAAAA,CAAAA,OAAS,GAAGK,QAAQ,GAC/BQ,WAAYb,EAAAA,CAAAA,CAAAA,OAAS,GAAGK,QAAQ,EAClC,GAAGA,QAAQ,GACXS,YAAad,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,GAAGe,EAAE,IAAIV,QAAQ,GAC9CW,gBAAiBhB,EAAAA,CAAAA,CAAAA,KAAO,CAACA,EAAAA,CAAAA,CAAAA,MAAQ,IAAIK,QAAQ,EAC/C,GAGO,eAAeY,EAAI9B,CAAoB,CAAE,QAAE+B,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMlB,EAAemB,kBAADnB,CAAoB,CAACd,GAE5D,GAAI,CAACgC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAErB,OAAQa,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,CAAEC,YAAU,WAAEC,CAAS,CAAE,CAAGZ,EACxC,OAAEa,CAAK,CAAE,CAAG,MAAMd,EAGlBe,EAAW/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAACC,GAEjD,CAAE+C,KAAMC,CAAM,OAAEX,CAAK,CAAE,CAAG,MAAMS,EACnCG,IAAI,CAAC,2BACLC,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;MAmBT,CAAC,EACAC,EAAE,CAAC,KAAMN,GACTO,MAAM,GAET,GAAIf,GAAS,CAACW,GAAU,EAAQK,kBAAkB,CAASC,OAAO,GAAKX,EAAYW,OAAO,CACxF,CAD0F,MACnFnB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,oBACTC,KAAM,kBACNC,KAAM,mBACR,CACF,EACA,CAAErB,OAAQ,GAAI,GAgBlB,OAXAL,EAAeyC,WAAW,CACxBb,EACA1C,EACA,CACEyC,CAJU3B,UAIE,IACZ0C,UAAW,qBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQtB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBwB,GAAIZ,EAAOY,EAAE,CACbC,OAAQ,UACR9C,SAAUiC,EAAOjC,QAAQ,CACzB+C,WAAYd,EAAOc,UAAU,CAC7BC,WAAY,GAAGf,EAAOc,UAAU,CAAC,CAAC,EAAE,IAAIE,MAAM,CAAC,IAAI,IAAI,CAAC,CACxDzC,YAAayB,EAAOzB,WAAW,CAC/BI,YAAaqB,EAAOrB,WAAW,CAC/BE,gBAAiBmB,EAAOnB,eAAe,CACvCoC,eAAgBjB,EAAOiB,cAAc,CACrCC,aAAclB,EAAOkB,YAAY,CACjCC,aAAcnB,EAAOmB,YAAY,CACjChD,OAAQ6B,EAAO7B,MAAM,CACrBC,WAAY4B,EAAO5B,UAAU,CAC7BgD,WAAYpB,EAAOoB,UAAU,CAC7BC,WAAYrB,EAAOqB,UAAU,CAC7BC,OAAQ,CACNV,GAAI,EAAQP,kBAAkB,CAASO,EAAE,CACzCvD,KAAM,EAAQgD,kBAAkB,CAAShD,IAAI,CAEjD,EAAG,CACDkE,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOlC,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAErB,OAAQ,GAAI,EAElB,CACF,CAGO,eAAeqD,EAAIxE,CAAoB,CAAE,QAAE+B,CAAM,CAAe,EACrE,GAAI,CAEF,IAAMC,EAAa,MAAMlB,EAAemB,kBAADnB,CAAoB,CAACd,GAE5D,GAAI,CAACgC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAErB,OAAQa,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGZ,EACxC,OAAEa,CAAK,CAAE,CAAG,MAAMd,EAGlB0C,EAAO,MAAMzE,EAAQoC,IAAI,GACzBsC,EAAmB9D,EAAmB+D,SAAS,CAACF,GAEtD,GAAI,CAACC,EAAiBxC,OAAO,CAC3B,CAD6B,MACtBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,uBACTC,KAAM,mBACNC,KAAM,qBACNoC,QAASF,EAAiBrC,KAAK,CAACwC,MAClC,CACF,EACA,CAAE1D,OAAQ,GAAI,GAIlB,IAAM2D,EAAaJ,EAAiB3B,IAAI,CAGlCD,EAAW/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAACC,GAEjD,CAAE+C,KAAMgC,CAAU,CAAE1C,OAAK,CAAE,CAAG,MAAMS,EACvCG,IAAI,CAAC,2BACL+B,MAAM,CAAC,CACN,GAAGF,CAAU,CACbT,WAAY,IAAIY,OAAOC,WAAW,EACpC,GACC/B,EAAE,CAAC,KAAMN,GACTM,EAAE,CAAC,UAAWR,EAAYW,OAAO,EACjCJ,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;MAiBT,CAAC,EACAE,MAAM,GAET,GAAIf,GAAS,CAAC0C,EACZ,OAAO5C,EAAAA,CADiB,CACLA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wCACTC,KAAM,kBACNC,KAAM,mBACR,CACF,EACA,CAAErB,OAAQ,GAAI,GAgBlB,OAXAL,EAAeyC,WAAW,CACxBb,EACA1C,EACA,CACEyC,CAJU3B,UAIE,IACZ0C,UAAW,qBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQtB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBwB,GAAImB,EAAWnB,EAAE,CACjBC,OAAQ,UACR9C,SAAUgE,EAAWhE,QAAQ,CAC7B+C,WAAYiB,EAAWjB,UAAU,CACjCC,WAAY,GAAGgB,EAAWjB,UAAU,CAAC,CAAC,EAAE,IAAIE,MAAM,CAAC,IAAI,IAAI,CAAC,CAC5DzC,YAAawD,EAAWxD,WAAW,CACnCI,YAAaoD,EAAWpD,WAAW,CACnCE,gBAAiBkD,EAAWlD,eAAe,CAC3CoC,eAAgBc,EAAWd,cAAc,CACzCC,aAAca,EAAWb,YAAY,CACrC/C,OAAQ4D,EAAW5D,MAAM,CACzBC,WAAY2D,EAAW3D,UAAU,CACjCgD,WAAYW,EAAWX,UAAU,CACjCC,WAAYU,EAAWV,UAAU,CACjCC,OAAQ,CACNV,GAAI,EAAYP,kBAAkB,CAASO,EAAE,CAC7CvD,KAAM,EAAYgD,kBAAkB,CAAShD,IAAI,CAErD,EAAG,CACDkE,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOlC,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAErB,OAAQ,GAAI,EAElB,CACF,CAGO,eAAegE,EAAOnF,CAAoB,CAAE,QAAE+B,CAAM,CAAe,EACxE,GAAI,CAEF,IAAMC,EAAa,MAAMlB,EAAemB,kBAADnB,CAAoB,CAACd,GAE5D,GAAI,CAACgC,EAAWE,OAAO,CACrB,CADuB,MAChBC,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAASN,EAAWK,KAAK,CACzBE,KAAM,uBACNC,KAAM,iBACR,CACF,EACA,CAAErB,OAAQa,EAAWS,UAAU,EAAI,GAAI,GAI3C,GAAM,YAAEC,CAAU,YAAEC,CAAU,WAAEC,CAAS,CAAE,CAAGZ,EACxC,OAAEa,CAAK,CAAE,CAAG,MAAMd,EAGlBe,EAAW/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAqCA,CAACC,GAEjD,CAAE+C,KAAMC,CAAM,CAAEX,MAAO+C,CAAU,CAAE,CAAG,MAAMtC,EAC/CG,IAAI,CAAC,2BACLC,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACAC,EAAE,CAAC,KAAMN,GACTO,MAAM,GAET,GAAIgC,GAAc,CAACpC,GAAU,EAAQK,kBAAkB,CAASC,OAAO,GAAKX,EAAYW,OAAO,CAC7F,CAD+F,MACxFnB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,oBACTC,KAAM,kBACNC,KAAM,mBACR,CACF,EACA,CAAErB,OAAQ,GAAI,GAKlB,GAAM,CAAEkB,MAAOgD,CAAW,CAAE,CAAG,MAAMvC,EAClCG,IAAI,CAAC,2BACL+B,MAAM,CAAC,CACN7D,OAAQ,UACRkD,WAAY,IAAIY,OAAOC,WAAW,EACpC,GACC/B,EAAE,CAAC,KAAMN,GACTM,EAAE,CAAC,UAAWR,EAAYW,OAAO,EAEpC,GAAI+B,EAEF,OAAOlD,EAAAA,EAFQ,CAEKC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,2BACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAErB,OAAQ,GAAI,GAgBlB,OAXAL,EAAeyC,WAAW,CACxBb,EACA1C,EACA,CACEyC,CAJU3B,UAIE,IACZ0C,UAAW,qBACXC,aAAc,WAChB,EACAb,GACAc,KAAK,CAACC,QAAQtB,KAAK,EAEdF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBwB,GAAIf,EACJgB,OAAQ,UACRyB,SAAS,EACThD,QAAS,iDACX,EAAG,CACDiC,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EAEF,CAAE,MAAOlC,EAAO,CAEd,OAAOF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,CACLC,QAAS,wBACTC,KAAM,eACNC,KAAM,gBACR,CACF,EACA,CAAErB,OAAQ,GAAI,EAElB,CACF,CAGO,eAAeoE,IACpB,OAAO,IAAIpD,EAAAA,EAAYA,CAAC,KAAM,CAC5BhB,OAAQ,IACRoD,QAAS,CACP,8BAA+B,IAC/B,+BAAgC,4BAChC,+BAAgC,wCAClC,CACF,EACF,CCrZA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,+CACA,6CACA,iBACA,uDACA,CAAK,CACL,sGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,wFACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,+CACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA0B,aAAe,kDAAyD,wOAAuQ,ySAAoU,mBAAmB,QAAQ,uDAA2D,gGAAwG,EAAE,oGAA4G,EAAE,kGAA0G,EAAE,+FAAuG,EAAE,uEAA+E,EAAE,kFAA0F,EAAE,0FAAkG,EAAE,uFAA+F,iBAAsB,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,gEAAoE,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAmsC,qBAAyB,ykDAAkmD,idAAge,OAAS,SAAS,qCAAyC,iCAAmC,WAAa,0CAAkD,MAAQ,YAAc,iBAAmB,sBAAwB,uBAiBruM,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/./src/lib/supabase/server.ts", "webpack://_N_E/./src/app/api/external/v1/api-keys/[keyId]/route.ts", "webpack://_N_E/./src/app/api/external/v1/api-keys/[keyId]/route.ts?647a", "webpack://_N_E/"], "sourcesContent": ["module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");", "import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { createClient } from '@supabase/supabase-js';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest } from 'next/server';\r\n\r\n// This is the standard setup for creating a Supabase server client\r\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\r\n// Updated for Next.js 15 async cookies requirement\r\nexport async function createSupabaseServerClientOnRequest() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return cookieStore.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          try {\r\n            cookieStore.set({ name, value, ...options });\r\n          } catch (error) {\r\n            // This error can be ignored if running in a Server Component\r\n            // where cookies can't be set directly. Cookie setting should be\r\n            // handled in Server Actions or Route Handlers.\r\n            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          try {\r\n            // To remove a cookie using the `set` method from `next/headers`,\r\n            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\r\n            cookieStore.set({ name, value: '', ...options });\r\n          } catch (error) {\r\n            // Similar to set, this might fail in a Server Component.\r\n            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Alternative method for API routes that need to handle cookies from request\r\nexport function createSupabaseServerClientFromRequest(request: NextRequest) {\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        get(name: string) {\r\n          return request.cookies.get(name)?.value;\r\n        },\r\n        set(name: string, value: string, options: CookieOptions) {\r\n          // In API routes, we can't set cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n        remove(name: string, options: CookieOptions) {\r\n          // In API routes, we can't remove cookies directly on the request\r\n          // This will be handled by the response\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n\r\n// Service role client for admin operations (OAuth token storage, etc.)\r\nexport function createServiceRoleClient() {\r\n  return createClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\r\n    {\r\n      auth: {\r\n        autoRefreshToken: false,\r\n        persistSession: false\r\n      }\r\n    }\r\n  );\r\n}\r\n", "import { type NextRequest, NextResponse } from 'next/server';\nimport { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';\nimport { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';\nimport { z } from 'zod';\n\n// Use Edge Runtime for better performance\nexport const runtime = 'edge';\n\nconst authMiddleware = new ApiKeyAuthMiddleware();\n\ninterface RouteParams {\n  params: Promise<{\n    keyId: string;\n  }>;\n}\n\n// Validation schema for updating API keys\nconst UpdateApiKeySchema = z.object({\n  key_name: z.string().min(1).max(100).optional(),\n  status: z.enum(['active', 'inactive']).optional(),\n  expires_at: z.string().datetime().nullable().optional(),\n  permissions: z.object({\n    chat: z.boolean().optional(),\n    streaming: z.boolean().optional(),\n    all_models: z.boolean().optional()\n  }).optional(),\n  allowed_ips: z.array(z.string().ip()).optional(),\n  allowed_domains: z.array(z.string()).optional()\n});\n\n// GET /api/external/v1/api-keys/{keyId} - Get specific API key\nexport async function GET(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { keyId } = await params;\n\n    // 2. Get API key\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: apiKey, error } = await supabase\n      .from('user_generated_api_keys')\n      .select(`\n        id,\n        key_name,\n        key_prefix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        last_used_ip,\n        status,\n        expires_at,\n        created_at,\n        updated_at,\n        custom_api_configs!inner(\n          id,\n          name,\n          user_id\n        )\n      `)\n      .eq('id', keyId)\n      .single();\n\n    if (error || !apiKey || (apiKey.custom_api_configs as any).user_id !== userConfig!.user_id) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'API key not found',\n            type: 'not_found_error',\n            code: 'api_key_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'api_key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: apiKey.id,\n      object: 'api_key',\n      key_name: apiKey.key_name,\n      key_prefix: apiKey.key_prefix,\n      masked_key: `${apiKey.key_prefix}_${'*'.repeat(28)}xxxx`,\n      permissions: apiKey.permissions,\n      allowed_ips: apiKey.allowed_ips,\n      allowed_domains: apiKey.allowed_domains,\n      total_requests: apiKey.total_requests,\n      last_used_at: apiKey.last_used_at,\n      last_used_ip: apiKey.last_used_ip,\n      status: apiKey.status,\n      expires_at: apiKey.expires_at,\n      created_at: apiKey.created_at,\n      updated_at: apiKey.updated_at,\n      config: {\n        id: (apiKey.custom_api_configs as any).id,\n        name: (apiKey.custom_api_configs as any).name\n      }\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in API key GET API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/external/v1/api-keys/{keyId} - Update API key\nexport async function PUT(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { keyId } = await params;\n\n    // 2. Validate request body\n    const body = await request.json();\n    const validationResult = UpdateApiKeySchema.safeParse(body);\n\n    if (!validationResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Invalid request data',\n            type: 'validation_error',\n            code: 'invalid_parameters',\n            details: validationResult.error.errors\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    const updateData = validationResult.data;\n\n    // 3. Update API key\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: updatedKey, error } = await supabase\n      .from('user_generated_api_keys')\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', keyId)\n      .eq('user_id', userConfig!.user_id)\n      .select(`\n        id,\n        key_name,\n        key_prefix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        status,\n        expires_at,\n        created_at,\n        updated_at,\n        custom_api_configs!inner(\n          id,\n          name\n        )\n      `)\n      .single();\n\n    if (error || !updatedKey) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'API key not found or failed to update',\n            type: 'not_found_error',\n            code: 'api_key_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'api_key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: updatedKey.id,\n      object: 'api_key',\n      key_name: updatedKey.key_name,\n      key_prefix: updatedKey.key_prefix,\n      masked_key: `${updatedKey.key_prefix}_${'*'.repeat(28)}xxxx`,\n      permissions: updatedKey.permissions,\n      allowed_ips: updatedKey.allowed_ips,\n      allowed_domains: updatedKey.allowed_domains,\n      total_requests: updatedKey.total_requests,\n      last_used_at: updatedKey.last_used_at,\n      status: updatedKey.status,\n      expires_at: updatedKey.expires_at,\n      created_at: updatedKey.created_at,\n      updated_at: updatedKey.updated_at,\n      config: {\n        id: (updatedKey.custom_api_configs as any).id,\n        name: (updatedKey.custom_api_configs as any).name\n      }\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in API key PUT API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/external/v1/api-keys/{keyId} - Revoke API key\nexport async function DELETE(request: NextRequest, { params }: RouteParams) {\n  try {\n    // 1. Authenticate using user-generated API key\n    const authResult = await authMiddleware.authenticateRequest(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        {\n          error: {\n            message: authResult.error,\n            type: 'authentication_error',\n            code: 'invalid_api_key'\n          }\n        },\n        { status: authResult.statusCode || 401 }\n      );\n    }\n\n    const { userApiKey, userConfig, ipAddress } = authResult;\n    const { keyId } = await params;\n\n    // 2. Verify API key exists and belongs to user\n    const supabase = createSupabaseServerClientFromRequest(request);\n    \n    const { data: apiKey, error: fetchError } = await supabase\n      .from('user_generated_api_keys')\n      .select(`\n        id,\n        key_name,\n        custom_api_configs!inner(\n          user_id\n        )\n      `)\n      .eq('id', keyId)\n      .single();\n\n    if (fetchError || !apiKey || (apiKey.custom_api_configs as any).user_id !== userConfig!.user_id) {\n      return NextResponse.json(\n        {\n          error: {\n            message: 'API key not found',\n            type: 'not_found_error',\n            code: 'api_key_not_found'\n          }\n        },\n        { status: 404 }\n      );\n    }\n\n    // 3. Revoke API key (set status to revoked instead of deleting)\n    const { error: revokeError } = await supabase\n      .from('user_generated_api_keys')\n      .update({\n        status: 'revoked',\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', keyId)\n      .eq('user_id', userConfig!.user_id);\n\n    if (revokeError) {\n      console.error('Error revoking API key:', revokeError);\n      return NextResponse.json(\n        {\n          error: {\n            message: 'Failed to revoke API key',\n            type: 'server_error',\n            code: 'database_error'\n          }\n        },\n        { status: 500 }\n      );\n    }\n\n    // 4. Log API usage\n    authMiddleware.logApiUsage(\n      userApiKey!,\n      request,\n      {\n        statusCode: 200,\n        modelUsed: 'api_key_management',\n        providerUsed: 'rokey_api',\n      },\n      ipAddress\n    ).catch(console.error);\n\n    return NextResponse.json({\n      id: keyId,\n      object: 'api_key',\n      revoked: true,\n      message: 'API key has been revoked and is no longer valid'\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      }\n    });\n\n  } catch (error) {\n    console.error('Error in API key DELETE API:', error);\n    return NextResponse.json(\n      {\n        error: {\n          message: 'Internal server error',\n          type: 'server_error',\n          code: 'internal_error'\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// OPTIONS handler for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n    },\n  });\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\api-keys\\\\[keyId]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/external/v1/api-keys/[keyId]/route\",\n        pathname: \"/api/external/v1/api-keys/[keyId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/external/v1/api-keys/[keyId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Ro<PERSON>ey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\external\\\\v1\\\\api-keys\\\\[keyId]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2F%5BkeyId%5D%2Froute&page=%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2F%5BkeyId%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2F%5BkeyId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&appPaths=%2Fapi%2Fexternal%2Fv1%2Fapi-keys%2F%5BkeyId%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/external/v1/api-keys/[keyId]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":true},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\",\"image/avif\"],\"dangerouslyAllowSVG\":true,\"contentSecurityPolicy\":\"default-src 'self'; script-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\RoKey App\\\\rokey-app\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":true,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":true,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"@heroicons/react\",\"@headlessui/react\",\"react-markdown\",\"react-syntax-highlighter\",\"@supabase/supabase-js\",\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\RoKey App\\\\rokey-app\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\",\"serverExternalPackages\":[\"pdf-parse\",\"mammoth\"],\"turbopack\":{\"rules\":{\"*.svg\":{\"loaders\":[\"@svgr/webpack\"],\"as\":\"*.js\"}},\"root\":\"C:\\\\RoKey App\\\\rokey-app\"},\"compiler\":{\"removeConsole\":true,\"reactRemoveProperties\":true},\"api\":{\"bodyParser\":{\"sizeLimit\":\"50mb\"},\"responseLimit\":\"50mb\"},\"_originalRedirects\":[]}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/external/v1/api-keys/[keyId]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/external/v1/api-keys/[keyId]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["createSupabaseServerClientFromRequest", "request", "createServerClient", "process", "cookies", "get", "name", "value", "set", "options", "remove", "runtime", "ApiKeyAuthMiddleware", "UpdateApiKeySchema", "z", "authMiddleware", "key_name", "min", "max", "optional", "status", "expires_at", "datetime", "nullable", "permissions", "chat", "streaming", "all_models", "allowed_ips", "ip", "allowed_domains", "GET", "params", "authResult", "authenticateRequest", "success", "NextResponse", "json", "error", "message", "type", "code", "statusCode", "userApiKey", "userConfig", "ip<PERSON><PERSON><PERSON>", "keyId", "supabase", "data", "<PERSON><PERSON><PERSON><PERSON>", "from", "select", "eq", "single", "custom_api_configs", "user_id", "logApiUsage", "modelUsed", "providerUsed", "catch", "console", "id", "object", "key_prefix", "masked_key", "repeat", "total_requests", "last_used_at", "last_used_ip", "created_at", "updated_at", "config", "headers", "PUT", "body", "validationResult", "safeParse", "details", "errors", "updateData", "updated<PERSON>ey", "update", "Date", "toISOString", "DELETE", "fetchError", "revokeError", "revoked", "OPTIONS"], "sourceRoot": "", "ignoreList": []}