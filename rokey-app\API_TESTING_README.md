# RouKey API Comprehensive Testing Guide

This directory contains comprehensive test scripts to validate your RouKey API deployment and ensure all features are working correctly with your API key: `rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM`

## 🚀 Quick Start

### Option 1: Run All Tests (Recommended)
```bash
chmod +x run-api-tests.sh
./run-api-tests.sh
```

### Option 2: Run Individual Tests

#### JavaScript/Node.js Tests
```bash
node test-api-key.js
```

#### Python Tests
```bash
# Install dependencies first
pip install requests

# Run tests
python3 test-comprehensive-api.py
```

## 📋 What Gets Tested

### 🔧 API Endpoints
- **Documentation**: `/api/external/v1/docs` - OpenAPI specification
- **Providers**: `/api/external/v1/providers` - List supported AI providers
- **Models**: `/api/external/v1/models` - Available AI models
- **Configurations**: `/api/external/v1/configs` - Your RouKey configurations
- **Usage Analytics**: `/api/external/v1/usage` - API usage statistics
- **API Keys**: `/api/external/v1/api-keys` - Manage your API keys

### ⚙️ Configuration Management
- List existing configurations
- Get specific configuration details
- View provider keys for configurations
- Check routing strategy settings

### 💬 Chat Completions
- **Basic Chat**: Simple conversations
- **Code Generation**: Programming tasks
- **Streaming Responses**: Real-time response streaming
- **Role-based Routing**: Using specific roles for intelligent routing
- **Agent Mode**: Complex multi-agent collaboration
- **Temperature Control**: Creative vs factual responses
- **Multi-turn Conversations**: Context-aware dialogues

### 🚀 Advanced Features
- **Smart Routing**: Automatic model selection based on task complexity
- **Multi-role Orchestration**: Combining multiple AI roles
- **Agent Mode Collaboration**: Multiple agents working together
- **Usage Analytics**: Detailed usage tracking and analytics
- **Rate Limiting**: Proper handling of API limits

## 🔑 API Key Configuration

Your API key is already configured in the test scripts:
- **API Key**: `rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM`
- **Domain**: `https://roukey.online`
- **API Base**: `https://roukey.online/api/external/v1`

## 📊 Test Results Interpretation

### ✅ Success Indicators
- **100% Success Rate**: All features working perfectly
- **80%+ Success Rate**: Most features working, minor issues
- **Authentication Success**: API key is valid and active

### ⚠️ Common Issues
- **Authentication Failures**: Check API key validity
- **Rate Limiting**: Tests include proper delays
- **Network Issues**: Verify internet connectivity
- **Configuration Issues**: Ensure you have proper configurations set up

## 🛠️ Troubleshooting

### API Key Issues
```bash
# Check API key format
echo "Your API key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM"
echo "Length: $(echo 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM' | wc -c)"
echo "Prefix: $(echo 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM' | cut -c1-8)"
```

### Network Connectivity
```bash
# Test basic connectivity
curl -I https://roukey.online

# Test API endpoint
curl -H "X-API-Key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM" \
     https://roukey.online/api/external/v1/providers
```

### Dependencies
```bash
# Node.js dependencies (none required)
node --version

# Python dependencies
pip install requests
python3 -c "import requests; print('Requests library available')"
```

## 📖 API Documentation

### Authentication
All requests require your API key in the `X-API-Key` header:
```bash
curl -H "X-API-Key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM" \
     https://roukey.online/api/external/v1/providers
```

### Chat Completions
```bash
curl -X POST https://roukey.online/api/external/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM" \
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

### Streaming Responses
```bash
curl -X POST https://roukey.online/api/external/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM" \
  -d '{
    "messages": [{"role": "user", "content": "Tell me a story"}],
    "stream": true,
    "max_tokens": 200
  }'
```

## 🎯 Expected Results

### Successful Test Run
```
🎉 ALL TESTS PASSED! Your RouKey API is working perfectly!
✨ Your API key has full access to all RouKey features.
🚀 You can now confidently integrate RouKey into your applications!
```

### Partial Success
```
✅ Most tests passed! Your RouKey API is working well.
⚠️  Some minor issues detected - check logs for details.
```

## 📞 Support

If you encounter issues:
- **Email**: <EMAIL>
- **Website**: https://roukey.online
- **Documentation**: https://roukey.online/docs

## 🔄 Regular Testing

Run these tests regularly to ensure:
- API key remains active
- All features continue working
- Performance is optimal
- New features are accessible

## 📝 Test Logs

All test scripts provide detailed logs including:
- Request/response details
- Timing information
- Error messages
- Success/failure indicators
- RouKey-specific metadata

Save test outputs for troubleshooting:
```bash
node test-api-key.js > test-results-$(date +%Y%m%d-%H%M%S).log 2>&1
```
