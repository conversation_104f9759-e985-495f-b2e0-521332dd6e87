"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6060],{6875:(e,s,a)=>{a.d(s,{L:()=>o,p:()=>c});var t=a(95155);a(12115);var r=a(55020),l=a(41448),n=a(83298),i=a(35695);function o(e){let{message:s="Unlock intelligent routing with Starter",variant:a="compact",className:o=""}=e,{subscriptionStatus:c,createCheckoutSession:d}=(0,n.R)(),m=(0,i.useRouter)();if(!c||"free"!==c.tier)return null;let x=async()=>{try{await d("starter")}catch(e){m.push("/pricing")}};return"compact"===a?(0,t.jsx)(r.PY1.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-lg p-3 ".concat(o),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-1.5 bg-orange-500/20 rounded-lg",children:(0,t.jsx)(l.B,{className:"h-4 w-4 text-orange-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-white",children:s}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Starter plan gives you AI-powered routing and 15 configurations."})]})]}),(0,t.jsx)("button",{onClick:x,className:"inline-flex items-center px-3 py-1.5 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200 whitespace-nowrap",children:"Get Starter"})]})}):(0,t.jsx)(r.PY1.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-xl p-6 ".concat(o),children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"p-2 bg-orange-500/20 rounded-xl",children:(0,t.jsx)(l.B,{className:"h-6 w-6 text-orange-400"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:s}),(0,t.jsx)("p",{className:"text-gray-300 mb-4",children:"Upgrade to Starter for intelligent routing, 15 configurations, and advanced features that optimize your AI costs and performance."}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:x,className:"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200",children:[(0,t.jsx)(l.K,{className:"w-4 h-4 mr-2"}),"Upgrade to Starter"]}),(0,t.jsx)("button",{onClick:()=>m.push("/pricing"),className:"inline-flex items-center px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 transition-colors duration-200",children:"View All Plans"})]})]})]})})}function c(e){let{className:s=""}=e,{subscriptionStatus:a}=(0,n.R)(),l=(0,i.useRouter)();return a&&"starter"===a.tier?(0,t.jsx)(r.PY1.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"text-center py-2 ".concat(s),children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Need unlimited configurations?"," ",(0,t.jsx)("button",{onClick:()=>l.push("/billing"),className:"text-orange-400 hover:text-orange-300 underline transition-colors",children:"Upgrade to Professional"})]})}):null}},8413:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(12115),l=a(60323);let n=new Map;function i(e){let{configId:s,onRetry:a,className:i="",disabled:o=!1}=e,[c,d]=(0,r.useState)(!1),[m,x]=(0,r.useState)([]),[u,g]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),[y,b]=(0,r.useState)("bottom"),f=(0,r.useRef)(null),j=(0,r.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=n.get(s);if(e&&Date.now()-e.timestamp<3e5){x(e.keys),p(!0);return}}g(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);n.set(s,{keys:a,timestamp:Date.now()}),x(a),p(!0)}}catch(e){}finally{g(!1)}}},[s]);(0,r.useEffect)(()=>{s&&!h&&j(!0)},[s,j,h]),(0,r.useEffect)(()=>{let e=e=>{f.current&&!f.current.contains(e.target)&&d(!1)};return c&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[c]);let v=e=>{d(!1),a(e)},N=(0,r.useCallback)(()=>{if(!f.current)return;let e=f.current.getBoundingClientRect(),s=window.innerHeight,a=e.top,t=s-e.bottom;t>=300||t>=a?b("bottom"):b("top")},[]);return(0,t.jsxs)("div",{className:"relative ".concat(i),ref:f,children:[(0,t.jsxs)("button",{onClick:()=>{c||0!==m.length||h||j(!0),c||N(),d(!c)},disabled:o,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(o?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,t.jsx)("svg",{className:"w-4 h-4 stroke-2 ".concat(u?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,t.jsx)(l.D,{className:"w-3 h-3 stroke-2"})]}),c&&(0,t.jsx)("div",{className:"absolute w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in ".concat("top"===y?"bottom-full left-0 mb-1 origin-bottom-left":"top-full left-0 mt-1 origin-top-left"),children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),j(!1)},disabled:u,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,t.jsx)("svg",{className:"w-3 h-3 ".concat(u?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),(0,t.jsxs)("button",{onClick:()=>v(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,t.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||u)&&(0,t.jsx)("div",{className:"border-t border-gray-100 my-1"}),u&&(0,t.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,t.jsxs)("button",{onClick:()=>v(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:u,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"font-medium",children:e.label}),(0,t.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!u&&0===m.length&&h&&(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!u&&(0,t.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,t.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=n.get(s);return e&&Date.now()-e.timestamp<3e5?(0,t.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},13741:(e,s,a)=>{a.d(s,{$:()=>n});var t=a(95155),r=a(12115),l=a(74338);let n=(0,r.forwardRef)((e,s)=>{let{className:a="",variant:r="default",size:n="default",loading:i=!1,icon:o,iconPosition:c="left",children:d,disabled:m,...x}=e,u={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},g=m||i;return(0,t.jsxs)("button",{ref:s,className:"".concat("inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[r]," ").concat({default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[n]," ").concat(a),disabled:g,...x,children:[i&&(0,t.jsx)(l.Ay,{size:"lg"===n?"md":"sm",className:"mr-2"}),!i&&o&&"left"===c&&(0,t.jsx)("span",{className:"".concat(u[n]," mr-2"),children:o}),d,!i&&o&&"right"===c&&(0,t.jsx)("span",{className:"".concat(u[n]," ml-2"),children:o})]})});n.displayName="Button"},14446:(e,s,a)=>{a.d(s,{Ay:()=>r,CE:()=>l});var t=a(95155);function r(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function l(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},35287:(e,s,a)=>{a.d(s,{z:()=>T});var t=a(95155),r=a(12115),l=a(13741),n=a(75898),i=a(56671),o=a(74466);let c=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-orange-600 text-white hover:bg-orange-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-700 border-gray-300"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:"".concat(c({variant:a})," ").concat(s||""),...r})}var m=a(87266),x=a(574);function u(e){var s,a,r,n;let{apiKey:o,onRevoke:c}=e,u=async e=>{try{await navigator.clipboard.writeText(e),i.oR.success("API key copied to clipboard")}catch(e){i.oR.error("Failed to copy API key")}},g=o.expires_at&&new Date(o.expires_at)<new Date,h="active"===o.status&&!g;return(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 ".concat(h?"":"opacity-75"),children:[(0,t.jsx)("div",{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:o.key_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-400",children:["Configuration: ",o.custom_api_configs.name]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d,{className:(e=>{switch(e){case"active":return"bg-green-900/30 text-green-300 border-green-500/50";case"inactive":return"bg-yellow-900/30 text-yellow-300 border-yellow-500/50";case"revoked":return"bg-red-900/30 text-red-300 border-red-500/50";default:return"bg-gray-800/50 text-gray-300 border-gray-600/50"}})(o.status),children:o.status}),g&&(0,t.jsx)(d,{className:"bg-red-900/30 text-red-300 border-red-500/50",children:"Expired"})]})]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"API Key (Masked)"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,t.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-300",children:[o.key_prefix,"_","*".repeat(28),"string"==typeof o.masked_key?o.masked_key.slice(-4):"xxxx"]}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>u("".concat(o.key_prefix,"_").concat("*".repeat(28)).concat("string"==typeof o.masked_key?o.masked_key.slice(-4):"xxxx")),className:"h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50",title:"Copy masked key (for reference only)",children:(0,t.jsx)(m.QR,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded",children:[(0,t.jsx)("span",{children:"⚠️"}),(0,t.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Permissions"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[o.permissions.chat&&(0,t.jsx)(d,{variant:"secondary",className:"text-xs bg-blue-900/30 text-blue-300 border-blue-500/50",children:"Chat Completions"}),o.permissions.streaming&&(0,t.jsx)(d,{variant:"secondary",className:"text-xs bg-green-900/30 text-green-300 border-green-500/50",children:"Streaming"}),o.permissions.all_models&&(0,t.jsx)(d,{variant:"secondary",className:"text-xs bg-purple-900/30 text-purple-300 border-purple-500/50",children:"All Models"})]})]}),(((null==(s=o.allowed_ips)?void 0:s.length)||0)>0||((null==(a=o.allowed_domains)?void 0:a.length)||0)>0)&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,t.jsx)(m.ek,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs",children:[((null==(r=o.allowed_ips)?void 0:r.length)||0)>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,t.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,t.jsx)("span",{children:o.allowed_ips.join(", ")})]}),((null==(n=o.allowed_domains)?void 0:n.length)||0)>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,t.jsx)(m.qz,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,t.jsx)("span",{children:o.allowed_domains.join(", ")})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,t.jsx)(m.Il,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Total Requests:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold text-white",children:(o.total_requests||0).toLocaleString()})]}),o.last_used_at&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Last Used:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold text-white",children:(0,x.m)(new Date(o.last_used_at),{addSuffix:!0})})]})]})]}),o.expires_at&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,t.jsx)(m.Vv,{className:"h-4 w-4"}),"Expiration"]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("span",{className:"font-semibold ".concat(g?"text-red-400":"text-white"),children:[new Date(o.expires_at).toLocaleDateString()," at"," ",new Date(o.expires_at).toLocaleTimeString()]}),!g&&(0,t.jsxs)("span",{className:"ml-2 text-gray-400",children:["(",(0,x.m)(new Date(o.expires_at),{addSuffix:!0}),")"]})]})]}),(0,t.jsx)("div",{className:"flex items-center justify-end pt-2 border-t border-gray-700",children:"revoked"!==o.status&&(0,t.jsxs)(l.$,{variant:"destructive",size:"sm",onClick:()=>c(o.id),className:"text-xs",children:[(0,t.jsx)(m.TB,{className:"h-3 w-3 mr-1"}),"Revoke"]})})]})]})}var g=a(47477),h=a(76288);let p=g.bL;g.l9;let y=g.ZL;g.bm;let b=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(g.hJ,{ref:s,className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ".concat(a||""),...r})});b.displayName=g.hJ.displayName;let f=r.forwardRef((e,s)=>{let{className:a,children:r,...l}=e;return(0,t.jsxs)(y,{children:[(0,t.jsx)(b,{}),(0,t.jsxs)(g.UC,{ref:s,className:"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg ".concat(a||""),...l,children:[r,(0,t.jsxs)(g.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(h.X,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=g.UC.displayName;let j=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:"flex flex-col space-y-1.5 text-center sm:text-left ".concat(s||""),...a})};j.displayName="DialogHeader";let v=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ".concat(s||""),...a})};v.displayName="DialogFooter";let N=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(g.hE,{ref:s,className:"text-lg font-semibold leading-none tracking-tight ".concat(a||""),...r})});N.displayName=g.hE.displayName;let w=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(g.VY,{ref:s,className:"text-sm text-gray-600 ".concat(a||""),...r})});w.displayName=g.VY.displayName;let k=(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:n,icon:i,iconPosition:o="left",id:c,...d}=e,m=c||"input-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsxs)("div",{className:"relative",children:[i&&"left"===o&&(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"h-5 w-5 text-gray-400",children:i})}),(0,t.jsx)("input",{ref:s,id:m,className:"\n              w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 \n              focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n              disabled:opacity-50 disabled:cursor-not-allowed\n              transition-all duration-200\n              ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n              ").concat(i&&"left"===o?"pl-10":"","\n              ").concat(i&&"right"===o?"pr-10":"","\n              ").concat(a,"\n            "),...d}),i&&"right"===o&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"h-5 w-5 text-gray-400",children:i})})]}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),n&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:n})]})});k.displayName="Input",(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:n,id:i,...o}=e,c=i||"textarea-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsx)("textarea",{ref:s,id:c,className:"\n            w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 \n            focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-all duration-200 resize-none\n            ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n            ").concat(a,"\n          "),...o}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),n&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:n})]})}).displayName="Textarea",(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:n,options:i=[],children:o,id:c,...d}=e,m=c||"select-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsxs)("select",{ref:s,id:m,className:"\n            w-full p-3 bg-white/5 border rounded-xl text-white \n            focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-all duration-200\n            ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n            ").concat(a,"\n          "),...d,children:[i.map(e=>(0,t.jsx)("option",{value:e.value,disabled:e.disabled,className:"bg-gray-800 text-white",children:e.label},e.value)),o]}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),n&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:n})]})}).displayName="Select";var _=a(85057);let C=(0,o.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-blue-50 text-blue-900 border-blue-200",destructive:"bg-red-50 text-red-900 border-red-200 [&>svg]:text-red-600"}},defaultVariants:{variant:"default"}}),A=r.forwardRef((e,s)=>{let{className:a,variant:r,...l}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:"".concat(C({variant:r})," ").concat(a||""),...l})});A.displayName="Alert",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:"mb-1 font-medium leading-none tracking-tight ".concat(a||""),...r})}).displayName="AlertTitle";let P=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:"text-sm [&_p]:leading-relaxed ".concat(a||""),...r})});P.displayName="AlertDescription";var S=a(31547);function R(e){let{open:s,onOpenChange:a,onCreateApiKey:n,configName:o,creating:c,subscriptionTier:d}=e,[m,x]=(0,r.useState)("form"),[u,g]=(0,r.useState)(null),[h,y]=(0,r.useState)(!0),[b,C]=(0,r.useState)(!1),[R,L]=(0,r.useState)({key_name:"",expires_at:""});(0,r.useEffect)(()=>{s||(x("form"),g(null),y(!0),C(!1),L({key_name:"",expires_at:""}))},[s]);let E=async e=>{if(e.preventDefault(),!R.key_name.trim())return void i.oR.error("Please enter a name for your API key");try{let e=await n({key_name:R.key_name.trim(),expires_at:R.expires_at||void 0});g(e),x("success")}catch(e){}},I=async()=>{if(null==u?void 0:u.api_key)try{await navigator.clipboard.writeText(u.api_key),C(!0),i.oR.success("API key copied to clipboard"),setTimeout(()=>C(!1),2e3)}catch(e){i.oR.error("Failed to copy API key")}},T=e=>{if("form"!==m||e){if("success"===m)return}else x("form"),g(null),y(!0),L({key_name:"",expires_at:""}),a(!1)};return"success"===m&&u?(0,t.jsx)(p,{open:s,onOpenChange:()=>{},modal:!0,children:(0,t.jsxs)(f,{className:"max-w-lg",children:[(0,t.jsxs)(j,{className:"text-center space-y-3",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(S.Uz,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)(N,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,t.jsx)(w,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)(A,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(S.hc,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(P,{className:"text-red-800 font-medium",children:[(0,t.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(_.J,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:h?u.api_key:"".concat(u.key_prefix,"_").concat("*".repeat(28)).concat(u.api_key.slice(-4))}),(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>y(!h),className:"h-8 w-8 p-0",title:h?"Hide key":"Show key",children:h?(0,t.jsx)(S.X_,{className:"h-4 w-4"}):(0,t.jsx)(S.kU,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:I,className:"h-8 w-8 p-0 ".concat(b?"text-green-600":""),title:"Copy to clipboard",children:b?(0,t.jsx)("span",{className:"text-xs",children:"✓"}):(0,t.jsx)(S.QR,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(l.$,{onClick:I,variant:"outline",className:"w-full",disabled:b,children:b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.QR,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(_.J,{className:"text-gray-600",children:"Key Name"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:u.key_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(_.J,{className:"text-gray-600",children:"Created"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:new Date(u.created_at).toLocaleString()})]})]})]}),(0,t.jsx)(v,{className:"pt-6",children:(0,t.jsx)(l.$,{onClick:()=>{x("form"),g(null),y(!0),C(!1),L({key_name:"",expires_at:""}),a(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,t.jsx)(p,{open:s,onOpenChange:T,children:(0,t.jsxs)(f,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(j,{children:[(0,t.jsxs)(N,{className:"flex items-center gap-2",children:[(0,t.jsx)(S.Uz,{className:"h-5 w-5"}),"Create API Key"]}),(0,t.jsxs)(w,{children:["Create a new API key for programmatic access to ",o]})]}),(0,t.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"key_name",children:"API Key Name *"}),(0,t.jsx)(k,{id:"key_name",value:R.key_name,onChange:e=>L(s=>({...s,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,t.jsx)(k,{id:"expires_at",type:"datetime-local",value:R.expires_at,onChange:e=>L(s=>({...s,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,t.jsxs)(v,{children:[(0,t.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>T(!1),children:"Cancel"}),(0,t.jsx)(l.$,{type:"submit",disabled:c,children:c?"Creating...":"Create API Key"})]})]})]})})}var L=a(6875),E=a(80377),I=a(87162);function T(e){let{configId:s,configName:a}=e,[o,c]=(0,r.useState)([]),[d,m]=(0,r.useState)(!0),[x,g]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),[y,b]=(0,r.useState)(null),f=(0,I.Z)(),j=async()=>{try{m(!0);let e=await fetch("/api/user-api-keys?config_id=".concat(s));if(!e.ok)throw Error("Failed to fetch API keys");let a=await e.json();c(a.api_keys||[])}catch(e){i.oR.error("Failed to load API keys")}finally{m(!1)}},v=async e=>{try{let s=await fetch("/api/user/subscription-tier"),a=s.ok?await s.json():null,t=(null==a?void 0:a.tier)||"starter",r={free:3,starter:50,professional:999999,enterprise:999999},l=void 0!==e?e:o.length;b({tier:t,keyLimit:r[t]||r.free,currentCount:l})}catch(s){b({tier:"free",keyLimit:3,currentCount:void 0!==e?e:o.length})}};(0,r.useEffect)(()=>{j()},[s]),(0,r.useEffect)(()=>{o.length>=0&&v()},[o]);let N=async e=>{try{g(!0);let t=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,custom_api_config_id:s})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create API key")}let r=await t.json();i.oR.success("API key created successfully!");let l={...r,custom_api_configs:{id:s,name:a},masked_key:r.api_key?r.api_key.slice(-4):"xxxx",allowed_ips:[],allowed_domains:[],total_requests:0,rate_limit_per_minute:0,rate_limit_per_hour:0,rate_limit_per_day:0,last_used_at:null,status:"active",encrypted_key_suffix:null};return c(e=>{let s=[l,...e];return v(s.length),s}),r}catch(e){throw i.oR.error(e.message||"Failed to create API key"),e}finally{g(!1)}},w=async e=>{let s=o.find(s=>s.id===e),a=(null==s?void 0:s.key_name)||"this API key";f.showConfirmation({title:"Revoke API Key",message:'Are you sure you want to revoke "'.concat(a,'"? This action cannot be undone and will immediately disable the key.'),confirmText:"Revoke Key",cancelText:"Cancel",type:"danger"},async()=>{try{let s=await fetch("/api/user-api-keys/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to revoke API key")}c(s=>s.map(s=>s.id===e?{...s,status:"revoked"}:s)),i.oR.success("API key revoked successfully")}catch(e){throw i.oR.error(e.message||"Failed to revoke API key"),e}})},k=!y||y.currentCount<y.keyLimit;return d?(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(n.e9,{className:"h-6 w-6 animate-spin mr-2 text-gray-400"}),(0,t.jsx)("span",{className:"text-gray-400",children:"Loading API keys..."})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2 text-white",children:[(0,t.jsx)(n.Uz,{className:"h-6 w-6"}),"API Keys"]}),(0,t.jsxs)("p",{className:"text-gray-400 mt-1",children:["Generate API keys for programmatic access to ",a]})]}),k?(0,t.jsxs)(l.$,{onClick:()=>p(!0),className:"flex items-center gap-2",children:[(0,t.jsx)(n.FW,{className:"h-4 w-4"}),"Create API Key"]}):(0,t.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,t.jsxs)(l.$,{disabled:!0,className:"flex items-center gap-2 opacity-50",children:[(0,t.jsx)(n.FW,{className:"h-4 w-4"}),"Create API Key"]}),(0,t.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:(null==y?void 0:y.tier)==="free"?"Upgrade to Starter plan for more API keys":"API key limit reached - upgrade for unlimited keys"})]})]}),(null==y?void 0:y.tier)==="free"&&(0,t.jsx)(L.L,{message:"Unlock intelligent routing and more API keys",variant:"compact",className:"mb-4"}),(0,t.jsx)(L.p,{className:"mb-4"}),0===o.length?(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-8",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)(n.Uz,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2 text-white",children:"No API Keys"}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),k?(0,t.jsxs)(l.$,{onClick:()=>p(!0),children:[(0,t.jsx)(n.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}):(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsxs)(l.$,{disabled:!0,className:"opacity-50",children:[(0,t.jsx)(n.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}),(0,t.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:(null==y?void 0:y.tier)==="free"?"Upgrade to Starter plan to create API keys":"API key limit reached - upgrade for unlimited keys"})]})]})}):(0,t.jsx)("div",{className:"grid gap-4",children:o.map(e=>(0,t.jsx)(u,{apiKey:e,onRevoke:w},e.id))}),(0,t.jsx)(R,{open:h,onOpenChange:e=>{p(e),e||j()},onCreateApiKey:N,configName:a,creating:x,subscriptionTier:(null==y?void 0:y.tier)||"starter"}),(0,t.jsx)(E.A,{isOpen:f.isOpen,onClose:f.hideConfirmation,onConfirm:f.onConfirm,title:f.title,message:f.message,confirmText:f.confirmText,cancelText:f.cancelText,type:f.type,isLoading:f.isLoading})]})}},38050:(e,s,a)=>{a.d(s,{default:()=>i});var t=a(12115),r=a(35695),l=a(21826),n=a(44042);function i(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:i=!0}=e,o=(0,r.usePathname)(),c=(0,t.useRef)(""),d=(0,t.useRef)(0),{exportMetrics:m}=(0,n.D)("PerformanceTracker");return(0,t.useEffect)(()=>{if(!a)return;let e=c.current;e&&e!==o&&(l.zf.trackNavigation(e,o),performance.now(),d.current),c.current=o,d.current=performance.now()},[o,a]),(0,t.useEffect)(()=>{if(!i)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&l.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?l.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?l.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&l.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[i]),(0,t.useEffect)(()=>{let e;if(!s)return;let a=!1,t=0,r=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;t=Math.max(t,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),t>80&&("/"===o?(l.zf.schedulePrefetch("/pricing"),l.zf.schedulePrefetch("/features")):"/features"===o&&l.zf.schedulePrefetch("/auth/signup")),t=0},150)},n=performance.now(),i=()=>{performance.now()-n>1e4&&("/"===o?l.zf.schedulePrefetch("/auth/signup"):"/pricing"===o&&l.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",r,{passive:!0});let c=()=>{document.hidden&&i()};document.addEventListener("visibilitychange",c);let d=()=>{i()};return window.addEventListener("beforeunload",d),()=>{clearTimeout(e),window.removeEventListener("scroll",r),document.removeEventListener("visibilitychange",c),window.removeEventListener("beforeunload",d),i()}},[o,s,m]),(0,t.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,t=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return t.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),t.disconnect()}}},[]),null}},50956:(e,s,a)=>{a.d(s,{A:()=>o});var t=a(95155),r=a(12115),l=a(6874),n=a.n(l),i=a(35695);function o(e){let{href:s,children:a,className:l="",prefetch:o=!0}=e,c=(0,i.useRouter)();return(0,t.jsx)(n(),{href:s,className:l,onClick:e=>{e.preventDefault(),(0,r.startTransition)(()=>{c.push(s)})},prefetch:o,children:a})}},60993:(e,s,a)=>{a.d(s,{Jg:()=>g,yA:()=>b,sU:()=>u});var t=a(95155);a(12115);var r=a(83298),l=a(74857),n=a(55020),i=a(39499),o=a(35695);let c={custom_roles:"Custom Roles",knowledge_base:"Knowledge Base",advanced_routing:"Advanced Routing",prompt_engineering:"Prompt Engineering",semantic_caching:"Semantic Caching",configurations:"API Configurations"},d=e=>{for(let s of["starter","professional"]){let a=l.v7[s];switch(e){case"custom_roles":if(a.limits.canUseCustomRoles)return s;break;case"knowledge_base":if(a.limits.canUseKnowledgeBase)return s;break;case"advanced_routing":if(a.limits.canUseAdvancedRouting)return s;break;case"prompt_engineering":if(a.limits.canUsePromptEngineering)return s;break;case"semantic_caching":if(a.limits.canUseSemanticCaching)return s;break;case"configurations":if(a.limits.configurations>l.v7.free.limits.configurations)return s}}return"starter"};function m(e){let{feature:s,currentTier:a,customMessage:m,size:x="md",variant:u="card",theme:g="light"}=e,{createCheckoutSession:h}=(0,r.R)(),p=(0,o.useRouter)(),y=d(s),b=l.v7[y],f=c[s],j=async()=>{try{"starter"===y?await h("starter"):"professional"===y?await h("professional"):p.push("/pricing")}catch(e){p.push("/pricing")}};return(0,t.jsx)(n.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"".concat({card:"dark"===g?"bg-gradient-to-br from-orange-900/20 to-orange-800/20 border border-orange-500/30 rounded-xl shadow-sm":"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm",banner:"dark"===g?"bg-orange-900/20 border-l-4 border-orange-500 rounded-r-lg":"bg-orange-100 border-l-4 border-orange-500 rounded-r-lg",inline:"dark"===g?"bg-orange-900/20 border border-orange-500/30 rounded-lg":"bg-orange-50 border border-orange-200 rounded-lg"}[u]," ").concat({sm:"p-4 text-sm",md:"p-6 text-base",lg:"p-8 text-lg"}[x]),children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center",children:(0,t.jsx)(i.JD,{className:"w-5 h-5 text-white"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-2 ".concat("dark"===g?"text-white":"text-gray-900"),children:[f," - Premium Feature"]}),(0,t.jsx)("p",{className:"mb-4 ".concat("dark"===g?"text-gray-300":"text-gray-700"),children:m||"".concat(f," is available starting with the ").concat(b.name," plan.\n               Upgrade to unlock this powerful feature and enhance your RouKey experience.")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:j,className:"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200",children:[(0,t.jsx)(i.Kp,{className:"w-4 h-4 mr-2"}),"Upgrade to ",b.name]}),(0,t.jsxs)("button",{onClick:()=>p.push("/pricing"),className:"inline-flex items-center px-4 py-2 text-sm font-medium transition-colors duration-200 ".concat("dark"===g?"text-orange-400 hover:text-orange-300":"text-orange-600 hover:text-orange-700"),children:[(0,t.jsx)(i.Gg,{className:"w-4 h-4 mr-2"}),"View All Plans"]})]})]})]})})}var x=a(74338);function u(e){let{feature:s,children:a,fallback:n,showUpgradePrompt:i=!0,customMessage:o,currentCount:c=0,theme:d="light"}=e,{subscriptionStatus:u,loading:g}=(0,r.R)();if(g)return(0,t.jsx)(x.Ay,{});let h=(null==u?void 0:u.tier)||"free";if("configurations"===s){if(c<(0,l.zX)(h).limits.configurations)return(0,t.jsx)(t.Fragment,{children:a})}else if((0,l.Nu)(h,s))return(0,t.jsx)(t.Fragment,{children:a});return n?(0,t.jsx)(t.Fragment,{children:n}):i?(0,t.jsx)(m,{feature:s,currentTier:h,customMessage:o,theme:d}):null}function g(e){let{current:s,limit:a,label:r,tier:l,showUpgradeHint:i=!0,className:c="",theme:d="light"}=e,m=(0,o.useRouter)(),x=a>=999999,u=x?0:s/a*100,g=u>=80,h=s>=a&&!x;return(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm ".concat(c),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"dark"===d?"text-gray-300":"text-gray-600",children:[r,":"]}),!x&&(0,t.jsx)("div",{className:"w-16 rounded-full h-1.5 ".concat("dark"===d?"bg-gray-700":"bg-gray-200"),children:(0,t.jsx)(n.PY1.div,{className:"h-1.5 rounded-full ".concat(x?"bg-green-500":h?"bg-red-500":g?"bg-yellow-500":"bg-green-500"),initial:{width:0},animate:{width:"".concat(Math.min(u,100),"%")},transition:{duration:.5,ease:"easeOut"}})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{className:"text-xs font-medium ".concat("dark"===d?x?"text-green-400":h?"text-red-400":g?"text-yellow-400":"text-green-400":x?"text-green-600":h?"text-red-600":g?"text-yellow-600":"text-green-600"),children:x?"Unlimited":"".concat(s,"/").concat(a)}),(h||g)&&i&&(0,t.jsx)("button",{className:"text-xs underline ml-2 ".concat("dark"===d?"text-orange-400 hover:text-orange-300":"text-orange-600 hover:text-orange-700"),onClick:()=>{m.push("/billing")},children:"Upgrade"})]})]})}a(47321);var h=a(17974);let p={free:{name:"Free",light:{color:"bg-gray-100 text-gray-800 border-gray-300",iconColor:"text-gray-600"},dark:{color:"bg-gray-800/50 text-gray-200 border-gray-600/50",iconColor:"text-gray-400"},icon:h.Zu},starter:{name:"Starter",light:{color:"bg-blue-100 text-blue-800 border-blue-300",iconColor:"text-blue-600"},dark:{color:"bg-blue-900/30 text-blue-200 border-blue-500/50",iconColor:"text-blue-400"},icon:h.Gg},professional:{name:"Professional",light:{color:"bg-orange-100 text-orange-800 border-orange-300",iconColor:"text-orange-600"},dark:{color:"bg-orange-900/30 text-orange-200 border-orange-500/50",iconColor:"text-orange-400"},icon:h.BZ},enterprise:{name:"Enterprise",light:{color:"bg-purple-100 text-purple-800 border-purple-300",iconColor:"text-purple-600"},dark:{color:"bg-purple-900/30 text-purple-200 border-purple-500/50",iconColor:"text-purple-400"},icon:h.OR}},y={sm:{container:"px-2 py-1 text-xs",icon:"w-3 h-3"},md:{container:"px-3 py-1 text-sm",icon:"w-4 h-4"},lg:{container:"px-4 py-2 text-base",icon:"w-5 h-5"}};function b(e){let{tier:s,size:a="md",showIcon:r=!0,className:l="",theme:n="light"}=e,i=p[s],o=y[a],c=i[n],d=i.icon;return(0,t.jsxs)("span",{className:"\n      inline-flex items-center space-x-1 font-medium border rounded-full\n      ".concat(c.color,"\n      ").concat(o.container,"\n      ").concat(l,"\n    "),children:[r&&(0,t.jsx)(d,{className:"".concat(o.icon," ").concat(c.iconColor)}),(0,t.jsx)("span",{children:i.name})]})}},64198:(e,s,a)=>{a.d(s,{N9:()=>i,dj:()=>o});var t=a(95155),r=a(12115),l=a(21884);let n=e=>{let{toast:s,onRemove:a}=e,[n,i]=(0,r.useState)(!1),[o,c]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setTimeout(()=>i(!0),10);return()=>clearTimeout(e)},[]),(0,r.useEffect)(()=>{if(s.duration&&s.duration>0){let e=setTimeout(()=>{d()},s.duration);return()=>clearTimeout(e)}},[s.duration]);let d=()=>{c(!0),setTimeout(()=>{a(s.id)},300)};return(0,t.jsx)("div",{className:"\n        transform transition-all duration-300 ease-in-out\n        ".concat(n&&!o?"translate-x-0 opacity-100":"translate-x-full opacity-0","\n        ").concat((()=>{let e="glass rounded-xl p-4 shadow-lg border";switch(s.type){case"success":return"".concat(e," border-green-500/20 bg-green-500/10");case"error":return"".concat(e," border-red-500/20 bg-red-500/10");case"warning":return"".concat(e," border-yellow-500/20 bg-yellow-500/10");case"info":return"".concat(e," border-blue-500/20 bg-blue-500/10")}})(),"\n      "),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(s.type){case"success":return(0,t.jsx)(l.C1,{className:"h-5 w-5 text-green-400"});case"error":return(0,t.jsx)(l.qh,{className:"h-5 w-5 text-red-400"});case"warning":return(0,t.jsx)(l.Pi,{className:"h-5 w-5 text-yellow-400"});case"info":return(0,t.jsx)(l.KS,{className:"h-5 w-5 text-blue-400"})}})()}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-white",children:s.title}),s.message&&(0,t.jsx)("p",{className:"text-sm text-gray-300 mt-1",children:s.message})]}),(0,t.jsx)("button",{onClick:d,className:"flex-shrink-0 p-1 rounded-lg hover:bg-white/10 transition-colors duration-200",children:(0,t.jsx)(l.fK,{className:"h-4 w-4 text-gray-400 hover:text-white"})})]})})},i=e=>{let{toasts:s,onRemove:a}=e;return(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full",children:s.map(e=>(0,t.jsx)(n,{toast:e,onRemove:a},e.id))})},o=()=>{let[e,s]=(0,r.useState)([]),a=e=>{var a;let t=Math.random().toString(36).substr(2,9),r={...e,id:t,duration:null!=(a=e.duration)?a:5e3};return s(e=>[...e,r]),t};return{toasts:e,addToast:a,removeToast:e=>{s(s=>s.filter(s=>s.id!==e))},success:(e,s,t)=>a({type:"success",title:e,message:s,duration:t}),error:(e,s,t)=>a({type:"error",title:e,message:s,duration:t}),warning:(e,s,t)=>a({type:"warning",title:e,message:s,duration:t}),info:(e,s,t)=>a({type:"info",title:e,message:s,duration:t})}}},69903:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(12115),l=a(35695),n=a(99323);let i=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),o=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,t.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),c=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"py-20",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,t.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),d=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),m=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),x=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function u(e){let s,{targetRoute:a,children:u}=e,[g,h]=(0,r.useState)(!0),[p,y]=(0,r.useState)(!1),b=(0,l.usePathname)(),f=(0,r.useRef)(),{isPageCached:j}=(0,n.bu)()||{isPageCached:()=>!1};return((0,r.useEffect)(()=>(b===a&&(f.current=setTimeout(()=>{y(!0),setTimeout(()=>h(!1),100)},j(a)?50:200)),()=>{f.current&&clearTimeout(f.current)}),[b,a,j]),(0,r.useEffect)(()=>{h(!0),y(!1)},[a]),b!==a&&g||b===a&&g&&!p)?(0,t.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,t.jsx)(i,{}):s.startsWith("/pricing")?(0,t.jsx)(o,{}):s.startsWith("/features")?(0,t.jsx)(c,{}):s.startsWith("/auth/")?(0,t.jsx)(d,{}):s.startsWith("/playground")?(0,t.jsx)(m,{}):(0,t.jsx)(x,{})}):(0,t.jsx)("div",{className:"transition-opacity duration-300 ".concat(p?"opacity-100":"opacity-0"),children:u})}},71848:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(23405);let l=e=>{let{label:s,value:a}=e;return(0,t.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-300",children:s}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-white sm:mt-0 sm:col-span-2 break-words",children:null!=a&&""!==a?a:"N/A"})]})},n=e=>{let s,{title:a,data:r}=e;if(null==r)s="N/A";else if("string"==typeof r)s=r;else try{s=JSON.stringify(r,null,2)}catch(e){s="Invalid JSON data"}return(0,t.jsxs)("div",{className:"py-2",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-300 mb-1",children:a}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-300 bg-gray-800/50 p-3 rounded-lg border border-gray-700",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function i(e){var s;let{log:a,onClose:i,apiConfigNameMap:o}=e;if(!a)return null;let c=a.custom_api_config_id?o[a.custom_api_config_id]||"Unknown Model":"N/A";return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:i,children:(0,t.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-800/50 max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Log Details (ID: ",a.id.substring(0,8),"...)"]}),(0,t.jsx)("button",{onClick:i,className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(r.f,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,t.jsxs)("dl",{className:"divide-y divide-gray-700",children:[(0,t.jsx)(l,{label:"Timestamp",value:new Date(a.request_timestamp).toLocaleString()}),(0,t.jsx)(l,{label:"API Model Used",value:c}),(0,t.jsx)(l,{label:"Role Requested",value:a.role_requested}),(0,t.jsx)(l,{label:"Role Used",value:a.role_used}),(0,t.jsx)(l,{label:"Status",value:null===(s=a.status_code)?(0,t.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,t.jsx)(l,{label:"LLM Provider",value:a.llm_provider_name}),(0,t.jsx)(l,{label:"LLM Model Name",value:(()=>{var e,s;if((null==(e=a.role_used)?void 0:e.includes("RouKey_Multi Roles_"))&&(null==(s=a.response_payload_summary)?void 0:s.models_used)){let e=a.response_payload_summary.models_used;return e.length<=3?e.join(", "):"".concat(e.slice(0,3).join(", "),"...")}return a.llm_model_name})()}),(0,t.jsx)(l,{label:"LLM Latency",value:null!==a.llm_provider_latency_ms?"".concat(a.llm_provider_latency_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"RoKey Latency",value:null!==a.processing_duration_ms?"".concat(a.processing_duration_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"Input Tokens",value:null!==a.input_tokens?a.input_tokens:"N/A"}),(0,t.jsx)(l,{label:"Output Tokens",value:null!==a.output_tokens?a.output_tokens:"N/A"}),(0,t.jsx)(l,{label:"Cost",value:null!==a.cost?"$".concat(a.cost.toFixed(6)):"N/A"}),(0,t.jsx)(l,{label:"Multimodal Request",value:a.is_multimodal?"Yes":"No"}),(0,t.jsx)(l,{label:"IP Address",value:a.ip_address}),a.user_id&&(0,t.jsx)(l,{label:"User ID",value:a.user_id}),a.error_message&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l,{label:"Error Message",value:a.error_message}),(0,t.jsx)(l,{label:"Error Source",value:a.error_source})]}),a.llm_provider_status_code&&(0,t.jsx)(l,{label:"LLM Provider Status",value:a.llm_provider_status_code})]}),a.request_payload_summary&&(0,t.jsx)(n,{title:"Request Payload Summary",data:a.request_payload_summary}),a.response_payload_summary&&(0,t.jsx)(n,{title:"Response Payload Summary",data:a.response_payload_summary}),a.error_details_zod&&(0,t.jsx)(n,{title:"Zod Validation Error Details",data:a.error_details_zod})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-800/50 text-right",children:(0,t.jsx)("button",{onClick:i,className:"px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200",children:"Close"})})]})})}},74338:(e,s,a)=>{a.d(s,{Ay:()=>r,B0:()=>l});var t=a(95155);function r(e){let{size:s="md",className:a=""}=e;return(0,t.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-600 border-t-indigo-500 ".concat({sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[s]," ").concat(a)})}function l(e){let{className:s=""}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl p-6 animate-pulse ".concat(s),children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}},78817:(e,s,a)=>{a.d(s,{A:()=>o});var t=a(95155);a(12115);var r=a(89732),l=a(99323),n=a(95565);let i={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:r.fA,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:r.RY,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:r.cu,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:r.sR,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:r.AQ,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:r.tl,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:r.r9,color:"text-pink-500",bgColor:"bg-pink-50"}};function o(e){let{targetRoute:s}=e,{clearNavigation:a}=(0,l.bu)()||{clearNavigation:()=>{}};if(!(s?i[s]:null))return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,t.jsx)(r.cu,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,t.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let o=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},s))}),(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),c=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,s)=>(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},s))})]});return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,t.jsx)(r.fK,{className:"w-5 h-5"})}),(()=>{switch(s){case"/dashboard":default:return(0,t.jsx)(n.O2,{});case"/my-models":return(0,t.jsx)(n.MyModelsSkeleton,{});case"/playground":return(0,t.jsx)(o,{});case"/routing-setup":return(0,t.jsx)(n.RoutingSetupSkeleton,{});case"/logs":return(0,t.jsx)(c,{});case"/training":return(0,t.jsx)(n.vD,{});case"/analytics":return(0,t.jsx)(n.AnalyticsSkeleton,{})}})()]})}},80377:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(95155),r=a(12115),l=a(38152);function n(e){let{isOpen:s,onClose:a,onConfirm:n,title:i,message:o,confirmText:c="Delete",cancelText:d="Cancel",type:m="danger",isLoading:x=!1}=e;(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&!x&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,x,a]);let u=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:l.uc};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.Pi};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.Pi}}})(),g=u.icon;return s?(0,t.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:x?void 0:a}),(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,t.jsx)("div",{className:"relative px-6 pt-6",children:(0,t.jsx)("button",{onClick:a,disabled:x,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,t.jsx)(l.fK,{className:"h-5 w-5"})})}),(0,t.jsxs)("div",{className:"px-6 pb-6",children:[(0,t.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,t.jsx)("div",{className:"".concat(u.iconBg," rounded-full p-3"),children:(0,t.jsx)(g,{className:"h-8 w-8 ".concat(u.iconColor)})})}),(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:o}),(0,t.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,t.jsx)("button",{type:"button",onClick:a,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:d}),(0,t.jsx)("button",{type:"button",onClick:n,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ".concat(u.confirmButton),children:x?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):c})]})]})]})})]}):null}},85057:(e,s,a)=>{a.d(s,{J:()=>i});var t=a(95155),r=a(12115),l=a(40968);let n=(0,a(74466).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:"".concat(n()," ").concat(a||""),...r})});i.displayName=l.b.displayName},95060:(e,s,a)=>{a.d(s,{A:()=>y});var t=a(95155),r=a(6874),l=a.n(r),n=a(66766),i=a(35695),o=a(12115),c=a(8652),d=a(18685),m=a(22261),x=a(99323),u=a(37843),g=a(24403),h=a(42724);let p=[{href:"/dashboard",label:"Dashboard",icon:c.fA,iconSolid:d.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:c.RY,iconSolid:d.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:c.cu,iconSolid:d.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:c.sR,iconSolid:d.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:c.AQ,iconSolid:d.AQ,description:"Request history"},{href:"/training",label:"Training",icon:c.tl,iconSolid:d.tl,description:"AI training & knowledge"},{href:"/analytics",label:"Analytics",icon:c.r9,iconSolid:d.r9,description:"Advanced insights"}];function y(){let e=(0,i.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:r,setHovered:c}=(0,m.c)(),{navigateOptimistically:d}=(0,x.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:y}=(0,u.C)(),{prefetchWhenIdle:b}=(0,u.e)(),{prefetchChatHistory:f}=(0,g.l2)(),{predictions:j,isLearning:v}=(0,h.x)(),N=(0,h.G)();(0,o.useEffect)(()=>{let s=p.map(e=>e.href),a=j.slice(0,2),t=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return b([...a,...t,...s.filter(s=>s!==e&&!a.includes(s)&&!t.includes(s)),"/playground","/logs"].slice(0,6))},[e,b,j,N,v]);let w=!s||a;return(0,t.jsxs)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] relative ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!r&&c(!0),onMouseLeave:()=>!r&&c(!1),children:[(0,t.jsx)("div",{className:"absolute top-0 right-0 h-full w-px bg-gradient-to-b from-gray-500/30 via-gray-400/40 to-gray-500/30"}),(0,t.jsx)("div",{className:"absolute top-0 right-0 h-full w-0.5 bg-gray-400/15 blur-sm"}),(0,t.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,t.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,t.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,t.jsxs)("div",{className:"relative overflow-hidden",children:[(0,t.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-black rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,t.jsx)(n.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:28,height:28,className:"object-contain"})}),(0,t.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,t.jsx)("nav",{className:"space-y-2",children:p.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),r=a?s.iconSolid:s.icon,n=j.includes(s.href),i=N.find(e=>e.route===s.href),o="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){y(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&f(e)}}}:y(s.href,50);return(0,t.jsx)(l(),{href:s.href,onClick:e=>{e.preventDefault(),d(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...o,children:(0,t.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,t.jsx)(r,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),n&&!a&&(0,t.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,t.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:s.label}),i&&!a&&(0,t.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===i.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===i.priority?"!":"\xb7"})]}),(0,t.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:i?i.reason:s.description})]})]})},s.href)})})]})})]})}},95494:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(6874),l=a.n(r),n=a(12115),i=a(27016),o=a(22261),c=a(35695),d=a(83298),m=a(52643),x=a(90441);function u(){var e,s,r,u,g,h,p;let{isCollapsed:y,isHovered:b,toggleSidebar:f}=(0,o.c)(),j=(0,c.usePathname)(),{user:v,subscriptionStatus:N}=(0,d.R)(),[w,k]=(0,n.useState)(!1),[_,C]=(0,n.useState)(!1),[A,P]=(0,n.useState)(!1),S=(0,m.createSupabaseBrowserClient)();(0,n.useEffect)(()=>{let e=()=>{P(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let R=(null==v||null==(e=v.user_metadata)?void 0:e.first_name)||(null==v||null==(r=v.user_metadata)||null==(s=r.full_name)?void 0:s.split(" ")[0])||"User",L=R.charAt(0).toUpperCase()+((null==v||null==(h=v.user_metadata)||null==(g=h.last_name)||null==(u=g.charAt(0))?void 0:u.toUpperCase())||(null==(p=R.charAt(1))?void 0:p.toUpperCase())||"U"),E=(e=>{switch(e){case"/dashboard":return{title:"Dashboard",subtitle:"Overview & analytics"};case"/playground":return{title:"Playground",subtitle:"Test your models"};case"/my-models":return{title:"My Models",subtitle:"API key management"};case"/routing-setup":return{title:"Routing Setup",subtitle:"Configure routing"};case"/tools":return{title:"Tool Connections",subtitle:"Manage connected tools"};case"/logs":return{title:"Logs",subtitle:"Request history"};case"/training":return{title:"Prompt Engineering",subtitle:"Custom prompts"};case"/analytics":return{title:"Analytics",subtitle:"Advanced insights"};case"/add-keys":return{title:"Add Keys",subtitle:"API key setup"};default:return{title:"Dashboard",subtitle:"Overview"}}})(j),I=(null==N?void 0:N.tier)==="free"?"Free Plan":(null==N?void 0:N.tier)==="starter"?"Starter Plan":(null==N?void 0:N.tier)==="professional"?"Professional Plan":"Free Plan",T=async()=>{try{let{clearAllUserCache:e}=await a.e(5738).then(a.bind(a,63171));await e(),await S.auth.signOut(),window.location.href="/auth/signin"}catch(e){try{localStorage.clear(),sessionStorage.clear()}catch(e){}window.location.href="/auth/signin"}};return(0,n.useEffect)(()=>{let e=e=>{(e.metaKey||e.ctrlKey)&&"k"===e.key&&(e.preventDefault(),C(!0))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]),(0,t.jsxs)("nav",{className:"header border-b border-gray-800/50 bg-[#040716] backdrop-blur-sm w-full",children:[(0,t.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 ".concat(A&&(!y||b)?"max-w-7xl mx-auto":A?"max-w-none":"max-w-7xl mx-auto"),children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:f,className:"lg:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200",title:"Toggle sidebar",children:(0,t.jsx)(i.tK,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"RouKey"})}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-400",children:[(0,t.jsx)("span",{children:E.title}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{className:"text-white font-medium",children:E.subtitle})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,t.jsxs)("div",{className:"hidden xl:block relative",children:[(0,t.jsxs)("button",{onClick:()=>C(!0),className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-gray-800/50 border border-gray-700 rounded-lg text-gray-400 hover:text-gray-200 hover:border-gray-600 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Search..."}),(0,t.jsx)("kbd",{className:"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-500 bg-gray-700 border border-gray-600 rounded",children:"⌘K"})]}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(i.$p,{className:"h-4 w-4 text-gray-500"})})]}),(0,t.jsx)("button",{onClick:()=>C(!0),className:"xl:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200",children:(0,t.jsx)(i.$p,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 relative",children:[(0,t.jsx)(i.XF,{className:"h-5 w-5 text-gray-400"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,t.jsxs)("div",{className:"hidden sm:block relative",children:[(0,t.jsxs)("button",{onClick:()=>k(!w),className:"p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex items-center space-x-1",children:[(0,t.jsx)(i.Vy,{className:"h-5 w-5 text-gray-400"}),(0,t.jsx)(i.D3,{className:"h-3 w-3 text-gray-400 transition-transform duration-200 ".concat(w?"rotate-180":"")})]}),w&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>k(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-20",children:[(0,t.jsxs)(l(),{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>k(!1),children:[(0,t.jsx)(i.Vy,{className:"h-4 w-4 mr-3 text-gray-400"}),"Account Settings"]}),(0,t.jsxs)(l(),{href:"/billing",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>k(!1),children:[(0,t.jsx)(i.BF,{className:"h-4 w-4 mr-3 text-gray-400"}),"Billing & Plans"]}),(0,t.jsxs)(l(),{href:"/docs",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>k(!1),children:[(0,t.jsx)(i.AQ,{className:"h-4 w-4 mr-3 text-gray-400"}),"Documentation"]}),(0,t.jsx)("hr",{className:"my-1 border-gray-700"}),(0,t.jsxs)("button",{onClick:T,className:"flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 transition-colors duration-200",children:[(0,t.jsx)(i.Rz,{className:"h-4 w-4 mr-3 text-red-400"}),"Sign Out"]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:L})}),(0,t.jsxs)("div",{className:"hidden md:block",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-white",children:R}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:I})]})]})]})]})}),(0,t.jsx)(x.K,{isOpen:_,onClose:()=>C(!1)})]})}},96364:(e,s,a)=>{a.d(s,{V:()=>l});var t=a(95155);a(12115);var r=a(82880);let l=e=>{let{senderName:s,roleId:a}=e,l=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let s=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(a),n=!a||"moderator"===a;return(0,t.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(l," flex items-center justify-center text-white shadow-sm animate-pulse"),children:(e=>e&&"moderator"!==e?(0,t.jsx)(r.Y,{className:"w-4 h-4"}):(0,t.jsx)(r.B,{className:"w-4 h-4"}))(a)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(n?"text-blue-700":"text-gray-700"),children:s}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(s)})]}),(0,t.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(n?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"),children:(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})}}}]);