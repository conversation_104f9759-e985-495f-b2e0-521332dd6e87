#!/usr/bin/env node

/**
 * Debug script for failing RouKey API endpoints
 * Investigates specific issues with models, configs, usage, and failed chat completions
 */

const API_KEY = 'rk_live_2b67e30f_esfv0yQeBKLnjdQLNU4MYLYrxNEyCXEM';
const BASE_URL = 'https://roukey.online';
const API_BASE = `${BASE_URL}/api/external/v1`;

// Helper function to make API requests with detailed error logging
async function debugApiRequest(endpoint, method = 'GET', body = null, description = '') {
  console.log(`\n🔍 DEBUGGING: ${description || method} ${endpoint}`);
  console.log('=' .repeat(60));
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
      'User-Agent': 'RouKey-Debug-Script/1.0'
    }
  };

  if (body && method !== 'GET') {
    options.body = JSON.stringify(body);
    console.log('📤 Request Body:', JSON.stringify(body, null, 2));
  }

  try {
    const startTime = Date.now();
    const response = await fetch(endpoint, options);
    const duration = Date.now() - startTime;

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Duration: ${duration}ms`);
    
    // Log all response headers for debugging
    console.log('📋 Response Headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }

    const responseText = await response.text();
    let responseData;

    try {
      responseData = JSON.parse(responseText);
      console.log('📥 Response JSON:', JSON.stringify(responseData, null, 2));
    } catch {
      console.log('📥 Raw Response:', responseText);
      responseData = responseText;
    }

    return { 
      success: response.ok, 
      status: response.status, 
      data: responseData, 
      response,
      headers: Object.fromEntries(response.headers.entries())
    };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    console.error('🔍 Error details:', error);
    return { success: false, error: error.message };
  }
}

// Test the failing endpoints specifically
async function debugFailingEndpoints() {
  console.log('🚀 DEBUGGING FAILING ROKEY API ENDPOINTS');
  console.log('='.repeat(60));
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`📡 API Base: ${API_BASE}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);

  // 1. Debug Models endpoint
  console.log('\n🤖 DEBUGGING MODELS ENDPOINT');
  console.log('─'.repeat(40));
  const modelsResult = await debugApiRequest(`${API_BASE}/models`, 'GET', null, 'List Available Models');
  
  // 2. Debug Configs endpoint  
  console.log('\n⚙️  DEBUGGING CONFIGS ENDPOINT');
  console.log('─'.repeat(40));
  const configsResult = await debugApiRequest(`${API_BASE}/configs`, 'GET', null, 'List Configurations');
  
  // 3. Debug Usage endpoint
  console.log('\n📊 DEBUGGING USAGE ENDPOINT');
  console.log('─'.repeat(40));
  const usageResult = await debugApiRequest(`${API_BASE}/usage`, 'GET', null, 'Get Usage Analytics');
  
  // 4. Debug Usage endpoint with parameters
  console.log('\n📊 DEBUGGING USAGE ENDPOINT WITH PARAMETERS');
  console.log('─'.repeat(40));
  const usageParams = new URLSearchParams({
    granularity: 'day',
    start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    end_date: new Date().toISOString()
  });
  const usageParamsResult = await debugApiRequest(`${API_BASE}/usage?${usageParams}`, 'GET', null, 'Get Usage Analytics (7 days)');

  // 5. Test the two failed chat completion scenarios
  console.log('\n💬 DEBUGGING FAILED CHAT COMPLETIONS');
  console.log('─'.repeat(40));
  
  // Test case that might have failed - Temperature test (factual)
  const factualTest = {
    messages: [{ role: 'user', content: 'What is the speed of light in vacuum?' }],
    temperature: 0.1,
    stream: false,
    max_tokens: 500
  };
  
  const factualResult = await debugApiRequest(`${API_BASE}/chat/completions`, 'POST', factualTest, 'Temperature Test (Factual)');
  
  // Test case that might have failed - Multi-turn conversation
  const multiTurnTest = {
    messages: [
      { role: 'user', content: 'What is blockchain technology?' },
      { role: 'assistant', content: 'Blockchain is a distributed ledger technology...' },
      { role: 'user', content: 'How does it ensure security?' }
    ],
    stream: false,
    max_tokens: 500
  };
  
  const multiTurnResult = await debugApiRequest(`${API_BASE}/chat/completions`, 'POST', multiTurnTest, 'Multi-turn Conversation');

  // 6. Test authentication with a simple endpoint
  console.log('\n🔐 DEBUGGING AUTHENTICATION');
  console.log('─'.repeat(40));
  const authTest = await debugApiRequest(`${API_BASE}/providers`, 'GET', null, 'Test Authentication');

  // Summary
  console.log('\n📋 DEBUG SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Providers (Auth Test): ${authTest.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`❓ Models: ${modelsResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`❓ Configs: ${configsResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`❓ Usage: ${usageResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`❓ Usage (with params): ${usageParamsResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`❓ Chat (Factual): ${factualResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`❓ Chat (Multi-turn): ${multiTurnResult.success ? 'SUCCESS' : 'FAILED'}`);

  // Detailed error analysis
  console.log('\n🔍 ERROR ANALYSIS');
  console.log('='.repeat(60));
  
  if (!modelsResult.success) {
    console.log('\n❌ MODELS ENDPOINT ISSUES:');
    console.log(`   Status: ${modelsResult.status}`);
    console.log(`   Error: ${JSON.stringify(modelsResult.data, null, 2)}`);
  }
  
  if (!configsResult.success) {
    console.log('\n❌ CONFIGS ENDPOINT ISSUES:');
    console.log(`   Status: ${configsResult.status}`);
    console.log(`   Error: ${JSON.stringify(configsResult.data, null, 2)}`);
  }
  
  if (!usageResult.success) {
    console.log('\n❌ USAGE ENDPOINT ISSUES:');
    console.log(`   Status: ${usageResult.status}`);
    console.log(`   Error: ${JSON.stringify(usageResult.data, null, 2)}`);
  }

  if (!factualResult.success) {
    console.log('\n❌ FACTUAL CHAT COMPLETION ISSUES:');
    console.log(`   Status: ${factualResult.status}`);
    console.log(`   Error: ${JSON.stringify(factualResult.data, null, 2)}`);
  }

  if (!multiTurnResult.success) {
    console.log('\n❌ MULTI-TURN CHAT COMPLETION ISSUES:');
    console.log(`   Status: ${multiTurnResult.status}`);
    console.log(`   Error: ${JSON.stringify(multiTurnResult.data, null, 2)}`);
  }

  console.log(`\n📅 Debug completed at: ${new Date().toISOString()}`);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the debug tests
debugFailingEndpoints().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
