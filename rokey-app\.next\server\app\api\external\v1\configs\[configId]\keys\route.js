(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[26],{3541:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>T,default:()=>M});var a,o={};r.r(o),r.d(o,{GET:()=>b,OPTIONS:()=>x,POST:()=>k,runtime:()=>h});var i={};r.r(i),r.d(i,{patchFetch:()=>A,routeModule:()=>w,serverHooks:()=>I,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>S});var s=r(8429),n=r(9874),c=r(8294),l=r(6567),p=r(4144),d=r(5421),u=r(974),m=r(4429),f=r(9975),g=r(770),_=r(1109);let h="edge",y=new m.S,v=_.z.object({provider:_.z.enum(["openai","anthropic","google","cohere","mistral","perplexity","groq","together","fireworks","deepseek"]),api_key:_.z.string().min(1),model_id:_.z.string().uuid().optional(),label:_.z.string().min(1).max(100),temperature:_.z.number().min(0).max(2).optional().default(.7),is_default_general_chat_model:_.z.boolean().optional().default(!1)});async function b(e,{params:t}){try{let r=await y.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:a,userConfig:o,ipAddress:i}=r,{configId:s}=await t,n=(0,f.Qb)(e),{data:c,error:l}=await n.from("custom_api_configs").select("id").eq("id",s).eq("user_id",o.user_id).single();if(l||!c)return u.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});let{data:p,error:d}=await n.from("api_keys").select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        last_used_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        )
      `).eq("custom_api_config_id",s).eq("user_id",o.user_id).order("created_at",{ascending:!1});if(d)return u.Rp.json({error:{message:"Failed to fetch provider keys",type:"server_error",code:"database_error"}},{status:500});return y.logApiUsage(a,e,{statusCode:200,modelUsed:"key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({object:"list",data:p||[],has_more:!1},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function k(e,{params:t}){try{let r=await y.authenticateRequest(e);if(!r.success)return u.Rp.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:a,userConfig:o,ipAddress:i}=r,{configId:s}=await t,n=await e.json(),c=v.safeParse(n);if(!c.success)return u.Rp.json({error:{message:"Invalid request data",type:"validation_error",code:"invalid_parameters",details:c.error.errors}},{status:400});let l=c.data,p=(0,f.Qb)(e),{data:d,error:m}=await p.from("custom_api_configs").select("id").eq("id",s).eq("user_id",o.user_id).single();if(m||!d)return u.Rp.json({error:{message:"Configuration not found",type:"not_found_error",code:"config_not_found"}},{status:404});let _=await (0,g.w)(l.api_key),h=new TextEncoder().encode(l.api_key),b=await crypto.subtle.digest("SHA-256",h),k=Array.from(new Uint8Array(b)).map(e=>e.toString(16).padStart(2,"0")).join(""),{data:x,error:w}=await p.from("api_keys").insert({custom_api_config_id:s,user_id:o.user_id,provider:l.provider,predefined_model_id:l.model_id||null,encrypted_api_key:_,label:l.label,api_key_hash:k,status:"active",is_default_general_chat_model:l.is_default_general_chat_model,temperature:l.temperature}).select(`
        id,
        provider,
        label,
        status,
        is_default_general_chat_model,
        temperature,
        created_at,
        updated_at,
        predefined_models(
          id,
          name,
          display_name,
          provider_id
        )
      `).single();if(w)return u.Rp.json({error:{message:"Failed to create provider key",type:"server_error",code:"database_error"}},{status:500});return y.logApiUsage(a,e,{statusCode:201,modelUsed:"key_management",providerUsed:"rokey_api"},i).catch(console.error),u.Rp.json({id:x.id,object:"provider_key",provider:x.provider,label:x.label,status:x.status,is_default_general_chat_model:x.is_default_general_chat_model,temperature:x.temperature,created_at:x.created_at,updated_at:x.updated_at,model:x.predefined_models},{status:201,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}catch(e){return u.Rp.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function x(){return new u.Rp(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key"}})}_.z.object({api_key:_.z.string().min(1).optional(),label:_.z.string().min(1).max(100).optional(),temperature:_.z.number().min(0).max(2).optional(),is_default_general_chat_model:_.z.boolean().optional(),status:_.z.enum(["active","inactive"]).optional()});let w=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/external/v1/configs/[configId]/keys/route",pathname:"/api/external/v1/configs/[configId]/keys",filename:"route",bundlePath:"app/api/external/v1/configs/[configId]/keys/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\configs\\[configId]\\keys\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:C,workUnitAsyncStorage:S,serverHooks:I}=w;function A(){return(0,d.V5)({workAsyncStorage:C,workUnitAsyncStorage:S})}let R=null==(a=self.__RSC_MANIFEST)?void 0:a["/api/external/v1/configs/[configId]/keys/route"],P=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);R&&P&&(0,n.fQ)({page:"/api/external/v1/configs/[configId]/keys/route",clientReferenceManifest:R,serverActionsManifest:P,serverModuleMap:(0,s.e)({serverActionsManifest:P})});let T=i,M=c.s.wrap(w,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},api:{bodyParser:{sizeLimit:"50mb"},responseLimit:"50mb"},_originalRedirects:[]}})},5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},9975:(e,t,r)=>{"use strict";r.d(t,{Qb:()=>o});var a=r(3339);function o(e){return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}r(2710)}},e=>{var t=t=>e(e.s=t);e.O(0,[580,918,109,44,833],()=>t(3541));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/configs/[configId]/keys/route"]=r}]);
//# sourceMappingURL=route.js.map