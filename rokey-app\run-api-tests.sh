#!/bin/bash

# RouKey API Test Runner
# Runs comprehensive tests for your RouKey API deployment

echo "🚀 RouKey API Test Runner"
echo "========================="
echo ""

# Check if Node.js is available
if command -v node &> /dev/null; then
    echo "✅ Node.js found - Running JavaScript tests..."
    echo ""
    node test-api-key.js
    echo ""
    echo "📋 JavaScript tests completed!"
else
    echo "⚠️  Node.js not found - Skipping JavaScript tests"
fi

echo ""
echo "─────────────────────────────────────────────────"
echo ""

# Check if Python is available
if command -v python3 &> /dev/null; then
    echo "✅ Python 3 found - Running Python tests..."
    echo ""
    
    # Check if requests library is installed
    if python3 -c "import requests" &> /dev/null; then
        python3 test-comprehensive-api.py
        echo ""
        echo "📋 Python tests completed!"
    else
        echo "⚠️  Python 'requests' library not found"
        echo "💡 Install it with: pip install requests"
        echo "🔄 Attempting to install requests..."
        
        if command -v pip3 &> /dev/null; then
            pip3 install requests
            if [ $? -eq 0 ]; then
                echo "✅ Requests installed successfully!"
                python3 test-comprehensive-api.py
                echo ""
                echo "📋 Python tests completed!"
            else
                echo "❌ Failed to install requests library"
            fi
        else
            echo "❌ pip3 not found - cannot install requests"
        fi
    fi
else
    echo "⚠️  Python 3 not found - Skipping Python tests"
fi

echo ""
echo "🎯 All available tests completed!"
echo "📞 For support, contact: <EMAIL>"
