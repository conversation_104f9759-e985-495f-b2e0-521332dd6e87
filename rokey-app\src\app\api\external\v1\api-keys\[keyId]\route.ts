import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Use Edge Runtime for better performance
export const runtime = 'edge';

const authMiddleware = new ApiKeyAuthMiddleware();

interface RouteParams {
  params: Promise<{
    keyId: string;
  }>;
}

// Validation schema for updating API keys
const UpdateApiKeySchema = z.object({
  key_name: z.string().min(1).max(100).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  expires_at: z.string().datetime().nullable().optional(),
  permissions: z.object({
    chat: z.boolean().optional(),
    streaming: z.boolean().optional(),
    all_models: z.boolean().optional()
  }).optional(),
  allowed_ips: z.array(z.string().ip()).optional(),
  allowed_domains: z.array(z.string()).optional()
});

// GET /api/external/v1/api-keys/{keyId} - Get specific API key
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { keyId } = await params;

    // 2. Get API key
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: apiKey, error } = await supabase
      .from('user_generated_api_keys')
      .select(`
        id,
        key_name,
        key_prefix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        last_used_ip,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name,
          user_id
        )
      `)
      .eq('id', keyId)
      .single();

    if (error || !apiKey || (apiKey.custom_api_configs as any).user_id !== userConfig!.user_id) {
      return NextResponse.json(
        {
          error: {
            message: 'API key not found',
            type: 'not_found_error',
            code: 'api_key_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'api_key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: apiKey.id,
      object: 'api_key',
      key_name: apiKey.key_name,
      key_prefix: apiKey.key_prefix,
      masked_key: `${apiKey.key_prefix}_${'*'.repeat(28)}xxxx`,
      permissions: apiKey.permissions,
      allowed_ips: apiKey.allowed_ips,
      allowed_domains: apiKey.allowed_domains,
      total_requests: apiKey.total_requests,
      last_used_at: apiKey.last_used_at,
      last_used_ip: apiKey.last_used_ip,
      status: apiKey.status,
      expires_at: apiKey.expires_at,
      created_at: apiKey.created_at,
      updated_at: apiKey.updated_at,
      config: {
        id: (apiKey.custom_api_configs as any).id,
        name: (apiKey.custom_api_configs as any).name
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in API key GET API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// PUT /api/external/v1/api-keys/{keyId} - Update API key
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { keyId } = await params;

    // 2. Validate request body
    const body = await request.json();
    const validationResult = UpdateApiKeySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request data',
            type: 'validation_error',
            code: 'invalid_parameters',
            details: validationResult.error.errors
          }
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // 3. Update API key
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: updatedKey, error } = await supabase
      .from('user_generated_api_keys')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', keyId)
      .eq('user_id', userConfig!.user_id)
      .select(`
        id,
        key_name,
        key_prefix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name
        )
      `)
      .single();

    if (error || !updatedKey) {
      return NextResponse.json(
        {
          error: {
            message: 'API key not found or failed to update',
            type: 'not_found_error',
            code: 'api_key_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'api_key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: updatedKey.id,
      object: 'api_key',
      key_name: updatedKey.key_name,
      key_prefix: updatedKey.key_prefix,
      masked_key: `${updatedKey.key_prefix}_${'*'.repeat(28)}xxxx`,
      permissions: updatedKey.permissions,
      allowed_ips: updatedKey.allowed_ips,
      allowed_domains: updatedKey.allowed_domains,
      total_requests: updatedKey.total_requests,
      last_used_at: updatedKey.last_used_at,
      status: updatedKey.status,
      expires_at: updatedKey.expires_at,
      created_at: updatedKey.created_at,
      updated_at: updatedKey.updated_at,
      config: {
        id: (updatedKey.custom_api_configs as any).id,
        name: (updatedKey.custom_api_configs as any).name
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in API key PUT API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// DELETE /api/external/v1/api-keys/{keyId} - Revoke API key
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;
    const { keyId } = await params;

    // 2. Verify API key exists and belongs to user
    const supabase = createSupabaseServerClientFromRequest(request);
    
    const { data: apiKey, error: fetchError } = await supabase
      .from('user_generated_api_keys')
      .select(`
        id,
        key_name,
        custom_api_configs!inner(
          user_id
        )
      `)
      .eq('id', keyId)
      .single();

    if (fetchError || !apiKey || (apiKey.custom_api_configs as any).user_id !== userConfig!.user_id) {
      return NextResponse.json(
        {
          error: {
            message: 'API key not found',
            type: 'not_found_error',
            code: 'api_key_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Revoke API key (set status to revoked instead of deleting)
    const { error: revokeError } = await supabase
      .from('user_generated_api_keys')
      .update({
        status: 'revoked',
        updated_at: new Date().toISOString()
      })
      .eq('id', keyId)
      .eq('user_id', userConfig!.user_id);

    if (revokeError) {
      console.error('Error revoking API key:', revokeError);
      return NextResponse.json(
        {
          error: {
            message: 'Failed to revoke API key',
            type: 'server_error',
            code: 'database_error'
          }
        },
        { status: 500 }
      );
    }

    // 4. Log API usage
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 200,
        modelUsed: 'api_key_management',
        providerUsed: 'rokey_api',
      },
      ipAddress
    ).catch(console.error);

    return NextResponse.json({
      id: keyId,
      object: 'api_key',
      revoked: true,
      message: 'API key has been revoked and is no longer valid'
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in API key DELETE API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
