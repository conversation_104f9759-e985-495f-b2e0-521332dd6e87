{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}}, "functions": {"/api/external/v1/api-keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/route.js"], "name": "app/api/external/v1/api-keys/route", "page": "/api/external/v1/api-keys/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys$", "originalSource": "/api/external/v1/api-keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/833.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/configs/[configId]/keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/[keyId]/route", "page": "/api/external/v1/configs/[configId]/keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]/keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/configs/[configId]/keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/route", "page": "/api/external/v1/configs/[configId]/keys/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys$", "originalSource": "/api/external/v1/configs/[configId]/keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/configs/[configId]/routing/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/routing/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/routing/route.js"], "name": "app/api/external/v1/configs/[configId]/routing/route", "page": "/api/external/v1/configs/[configId]/routing/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/routing$", "originalSource": "/api/external/v1/configs/[configId]/routing"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/docs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/docs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/app/api/external/v1/docs/route.js"], "name": "app/api/external/v1/docs/route", "page": "/api/external/v1/docs/route", "matchers": [{"regexp": "^/api/external/v1/docs$", "originalSource": "/api/external/v1/docs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/providers/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/providers/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/833.js", "server/app/api/external/v1/providers/route.js"], "name": "app/api/external/v1/providers/route", "page": "/api/external/v1/providers/route", "matchers": [{"regexp": "^/api/external/v1/providers$", "originalSource": "/api/external/v1/providers"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/configs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/route.js"], "name": "app/api/external/v1/configs/route", "page": "/api/external/v1/configs/route", "matchers": [{"regexp": "^/api/external/v1/configs$", "originalSource": "/api/external/v1/configs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/usage/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/usage/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/usage/route.js"], "name": "app/api/external/v1/usage/route", "page": "/api/external/v1/usage/route", "matchers": [{"regexp": "^/api/external/v1/usage$", "originalSource": "/api/external/v1/usage"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/api-keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/[keyId]/route.js"], "name": "app/api/external/v1/api-keys/[keyId]/route", "page": "/api/external/v1/api-keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/api-keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/configs/[configId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/route.js"], "name": "app/api/external/v1/configs/[configId]/route", "page": "/api/external/v1/configs/[configId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}, "/api/external/v1/models/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/models/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/models/route.js"], "name": "app/api/external/v1/models/route", "page": "/api/external/v1/models/route", "matchers": [{"regexp": "^/api/external/v1/models$", "originalSource": "/api/external/v1/models"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hqGo6CODrs8v0n5c07YOv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "c5012107afd37f40a30285a3bafe770a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaac67447e4cef49bb90d5c45555f4356cead8cd3ececc17c553ead90fcb1dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf84bd482e53da7344abdae1bfc1ddc4899d0cac0725b479cf63cbd399f709b8"}}}, "sortedMiddleware": ["/"]}